GIT
  remote: *****************:uownco/uown-core.git
  revision: 41c597f635886b8111d3b7d5d96e3e775826cab0
  tag: v6.5.21
  specs:
    uown_core (6.5.21)
      aasm (= 5.2.0)
      active_delivery (~> 1.2)
      active_storage_validations (= 1.0.1)
      after_commit_everywhere (~> 1.0)
      authy (= 3.0.0)
      aws-sdk-s3 (= 1.105.1)
      bcrypt (= 3.1.12)
      countries (= 4.0.1)
      haml-rails (= 2.0.1)
      ibandit
      image_processing
      mangopay (= 3.8.0)
      noticed (= 2.2.2)
      password_strength (= 1.1.4)
      rails (~> 7.0.1)
      rest-client
      sidekiq (= 7.2.4)
      sprockets-rails
      uk_postcode (= 2.1.6)
      uuid7

GEM
  remote: https://rubygems.org/
  specs:
    aasm (5.2.0)
      concurrent-ruby (~> 1.0)
    actioncable (7.0.1)
      actionpack (= 7.0.1)
      activesupport (= 7.0.1)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (7.0.1)
      actionpack (= 7.0.1)
      activejob (= 7.0.1)
      activerecord (= 7.0.1)
      activestorage (= 7.0.1)
      activesupport (= 7.0.1)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.0.1)
      actionpack (= 7.0.1)
      actionview (= 7.0.1)
      activejob (= 7.0.1)
      activesupport (= 7.0.1)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (7.0.1)
      actionview (= 7.0.1)
      activesupport (= 7.0.1)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (7.0.1)
      actionpack (= 7.0.1)
      activerecord (= 7.0.1)
      activestorage (= 7.0.1)
      activesupport (= 7.0.1)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.0.1)
      activesupport (= 7.0.1)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_delivery (1.2.0)
      ruby-next-core (~> 1.0)
    active_model_serializers (0.10.15)
      actionpack (>= 4.1)
      activemodel (>= 4.1)
      case_transform (>= 0.2)
      jsonapi-renderer (>= 0.1.1.beta1, < 0.3)
    active_storage_validations (1.0.1)
      activejob (>= 5.2.0)
      activemodel (>= 5.2.0)
      activestorage (>= 5.2.0)
      activesupport (>= 5.2.0)
    activejob (7.0.1)
      activesupport (= 7.0.1)
      globalid (>= 0.3.6)
    activemodel (7.0.1)
      activesupport (= 7.0.1)
    activerecord (7.0.1)
      activemodel (= 7.0.1)
      activesupport (= 7.0.1)
    activestorage (7.0.1)
      actionpack (= 7.0.1)
      activejob (= 7.0.1)
      activerecord (= 7.0.1)
      activesupport (= 7.0.1)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (7.0.1)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    after_commit_everywhere (1.5.0)
      activerecord (>= 4.2)
      activesupport
    airbrake (13.0.5)
      airbrake-ruby (~> 6.0)
    airbrake-ruby (6.2.2)
      rbtree3 (~> 0.6)
    ast (2.4.2)
    authy (3.0.0)
      httpclient (>= *******)
    aws-eventstream (1.3.0)
    aws-partitions (1.1034.0)
    aws-sdk-core (3.214.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.96.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.105.1)
      aws-sdk-core (~> 3, >= 3.122.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sigv4 (1.10.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.12)
    bigdecimal (3.1.9)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    builder (3.3.0)
    byebug (11.1.3)
    case_transform (0.2)
      activesupport
    childprocess (5.1.0)
      logger (~> 1.5)
    concurrent-ruby (1.3.4)
    connection_pool (2.5.0)
    countries (4.0.1)
      i18n_data (~> 0.13.0)
      sixarm_ruby_unaccent (~> 1.1)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csv (3.3.2)
    date (3.4.1)
    diff-lcs (1.5.1)
    docile (1.4.1)
    domain_name (0.6.20240107)
    erubi (1.13.1)
    erubis (2.7.0)
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    faraday (2.12.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ffi (1.17.1-x86_64-linux-gnu)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    haml (5.2.2)
      temple (>= 0.8.0)
      tilt
    haml-rails (2.0.1)
      actionpack (>= 5.1)
      activesupport (>= 5.1)
      haml (>= 4.0.6, < 6.0)
      html2haml (>= 1.0.1)
      railties (>= 5.1)
    haml_lint (0.37.1)
      haml (>= 4.0, < 5.3)
      parallel (~> 1.10)
      rainbow
      rubocop (>= 0.50.0)
      sysexits (~> 1.1)
    hashdiff (1.1.2)
    html2haml (2.3.0)
      erubis (~> 2.7.0)
      haml (>= 4.0)
      nokogiri (>= 1.6.0)
      ruby_parser (~> 3.5)
    http-accept (1.7.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    httpclient (2.8.3)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    i18n_data (0.13.1)
    ibandit (1.25.0)
      i18n
    image_processing (1.13.0)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    jmespath (1.6.2)
    json (2.9.1)
    json-schema (5.1.1)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
    jsonapi-renderer (0.2.2)
    jwt (2.10.1)
      base64
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.6.5)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    mangopay (3.8.0)
      multi_json (>= 1.7.7)
    marcel (1.0.4)
    method_source (1.1.0)
    mime-types (3.6.0)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2025.0107)
    mini_magick (4.13.2)
    mini_mime (1.1.5)
    minitest (5.25.4)
    msgpack (1.7.5)
    multi_json (1.15.0)
    mysql2 (0.5.4)
    net-http (0.6.0)
      uri
    net-imap (0.5.5)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    netrc (0.11.0)
    nio4r (2.7.4)
    nokogiri (1.18.1-x86_64-linux-gnu)
      racc (~> 1.4)
    noticed (2.2.2)
      rails (>= 6.1.0)
    parallel (1.26.3)
    parser (3.3.6.0)
      ast (~> 2.4.1)
      racc
    password_strength (1.1.4)
      activemodel
    phony (2.21.1)
    phony_rails (0.15.0)
      activesupport (>= 3.0)
      phony (>= 2.18.12)
    posthog-ruby (2.5.1)
      concurrent-ruby (~> 1)
    psych (5.2.2)
      date
      stringio
    public_suffix (6.0.1)
    puma (5.6.9)
      nio4r (~> 2.0)
    pundit (2.4.0)
      activesupport (>= 3.0.0)
    racc (1.8.1)
    rack (2.2.10)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rails (7.0.1)
      actioncable (= 7.0.1)
      actionmailbox (= 7.0.1)
      actionmailer (= 7.0.1)
      actionpack (= 7.0.1)
      actiontext (= 7.0.1)
      actionview (= 7.0.1)
      activejob (= 7.0.1)
      activemodel (= 7.0.1)
      activerecord (= 7.0.1)
      activestorage (= 7.0.1)
      activesupport (= 7.0.1)
      bundler (>= 1.15.0)
      railties (= 7.0.1)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (7.0.1)
      actionpack (= 7.0.1)
      activesupport (= 7.0.1)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rbtree3 (0.7.1)
    rdoc (6.10.0)
      psych (>= 4.0.0)
    redis-client (0.23.0)
      connection_pool
    regexp_parser (2.10.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.4.0)
    rspec-core (3.13.2)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (5.0.3)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      railties (>= 5.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-support (3.13.2)
    rswag (2.16.0)
      rswag-api (= 2.16.0)
      rswag-specs (= 2.16.0)
      rswag-ui (= 2.16.0)
    rswag-api (2.16.0)
      activesupport (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rswag-specs (2.16.0)
      activesupport (>= 5.2, < 8.1)
      json-schema (>= 2.2, < 6.0)
      railties (>= 5.2, < 8.1)
      rspec-core (>= 2.14)
    rswag-ui (2.16.0)
      actionpack (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rubocop (1.22.3)
      parallel (~> 1.10)
      parser (>= 3.0.0.0)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml
      rubocop-ast (>= 1.12.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 3.0)
    rubocop-ast (1.37.0)
      parser (>= *******)
    rubocop-dubit (1.0.1)
      haml_lint (~> 0.37.1)
      rubocop (~> 1.22.2)
      rubocop-performance (~> 1.11.5)
      rubocop-rails (~> 2.12.4)
      rubocop-rspec (~> 2.5.0)
    rubocop-performance (1.11.5)
      rubocop (>= 1.7.0, < 2.0)
      rubocop-ast (>= 0.4.0)
    rubocop-rails (2.12.4)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.7.0, < 2.0)
    rubocop-rspec (2.5.0)
      rubocop (~> 1.19)
    ruby-next-core (1.1.1)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.2)
      ffi (~> 1.12)
      logger
    ruby_parser (3.21.1)
      racc (~> 1.5)
      sexp_processor (~> 4.16)
    sdoc (2.6.1)
      rdoc (>= 5.0)
    sexp_processor (4.17.3)
    shoulda-matchers (5.0.0)
      activesupport (>= 5.2.0)
    sidekiq (7.2.4)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      rack (>= 2.2.4)
      redis-client (>= 0.19.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    sixarm_ruby_unaccent (1.2.2)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sqlite3 (1.7.3-x86_64-linux)
    stringio (3.1.2)
    sysexits (1.2.0)
    temple (0.10.3)
    thor (1.3.2)
    tilt (2.5.0)
    timeout (0.4.3)
    twilio-ruby (7.4.1)
      faraday (>= 0.9, < 3.0)
      jwt (>= 1.5, < 3.0)
      nokogiri (>= 1.6, < 2.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2024.2)
      tzinfo (>= 1.0.0)
    uk_postcode (2.1.6)
    unicode-display_width (2.6.0)
    uri (1.0.2)
    uuid7 (0.2.0)
      zeitwerk (~> 2.4)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.24.0)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.1)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  active_model_serializers
  airbrake (~> 13.0.4)
  bootsnap (>= 1.4.4)
  byebug
  factory_bot
  faraday
  geocoder
  letter_opener
  listen (~> 3.3)
  mysql2 (= 0.5.4)
  phony_rails
  posthog-ruby
  puma (~> 5.0)
  pundit
  rack-cors
  rails (= 7.0.1)
  rails-controller-testing
  rspec-rails (~> 5.0.2)
  rswag (~> 2.16.0)
  rubocop-dubit (~> 1.0.1)
  sdoc
  shoulda-matchers (~> 5.0.0)
  simplecov
  sqlite3 (~> 1.4)
  twilio-ruby
  tzinfo-data
  uown_core!
  web-console (>= 4.1.0)
  webmock

RUBY VERSION
   ruby 3.3.6p108

BUNDLED WITH
   2.5.22
