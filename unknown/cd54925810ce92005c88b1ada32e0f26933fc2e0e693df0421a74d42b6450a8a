require 'rails_helper'

RSpec.describe Nuapay::Payments::Create, type: :service do
  let(:user) { create(:user_natural) }
  let(:bank_id) { 'bank_123' }
  let(:bank_wire_reference) { 'bank_wire_reference_123' }
  let(:log_id) { 'payment_log_id_123'}
  let(:p_inv_id) { 'p_inv_123' }
  let(:amount) { 100.0 }
  let(:req_body) do
    { amount: amount,
      currency: 'GBP',
      countryCode: 'GB',
      remittanceInformation: {
        reference: bank_wire_reference
      },
      email: nil,
      language: 'en',
      endToEndIdentification: "payment_log_id_123p_inv_123",
      orderDetails: nil,
      integrationType: 'SELF_HOSTED',
      merchantPostAuthUrl: "#{Rails.application.secrets.frontend_url}/payins/open_bankings/handle_response?p_inv_id=#{p_inv_id}&log_id=#{log_id}&user_id=#{user.id}",
      bankId: bank_id }
  end
  let(:service) { described_class.new(amount: amount, bank_id: bank_id, user: user, params: {}, p_inv_id: p_inv_id) }
  before do
    allow(Mango::BankWire).to receive(:create).and_return(double(log_id: log_id, reference: bank_wire_reference))
  end
  describe '#initialize' do
    it 'assigns all parameters correctly' do
      expect(service.instance_variable_get(:@amount)).to eq(amount)
      expect(service.instance_variable_get(:@bank_id)).to eq(bank_id)
      expect(service.instance_variable_get(:@user)).to eq(user)
      expect(service.instance_variable_get(:@params)).to eq(req_body)
      expect(service.instance_variable_get(:@p_inv_id)).to eq(p_inv_id)
    end
  end

  describe '#call' do
    it 'sets the Idempotency-Key header' do
      allow(SecureRandom).to receive(:alphanumeric).with(Nuapay::Payments::Create::IDEMPOTENCY_KEY_LENGTH).and_return('unique_key_12345')
      allow(NuapayClient).to receive(:headers).and_return({})

      service.call

      expect(NuapayClient.headers['Idempotency-Key']).to eq('unique_key_12345')
    end
  end

  describe '#req_body' do
    it 'returns the correct request body' do
      expect(service.send(:req_body)).to eq(req_body)
    end
  end

  describe '#generate_idempotency_key' do
    it 'generates a unique alphanumeric key of the correct length' do
      expect(SecureRandom).to receive(:alphanumeric).with(Nuapay::Payments::Create::IDEMPOTENCY_KEY_LENGTH).and_return('idempotent_key')

      expect(service.send(:generate_idempotency_key)).to eq('idempotent_key')
    end
  end
end
