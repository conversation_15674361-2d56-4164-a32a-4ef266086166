require 'rails_helper'

RSpec.describe Nuapay::Payments::Retrieve do
  let(:payment_id) { '12345' }
  let(:service) { described_class.new(payment_id: payment_id) }

  describe '#initialize' do
    it 'sets the payment_id' do
      expect(service.instance_variable_get(:@payment_id)).to eq(payment_id)
    end
  end

  describe '#sub_url' do
    it 'returns the correct sub_url' do
      expect(service.send(:sub_url)).to eq("nuapay/payments/#{payment_id}")
    end
  end
end