# Prerequisites - The following repository variables must be defined.
# The values for each are provided as an example only and may be different for your project.
# -------------------------------
# Global
# -------------------------------
# $DOCKER_IMAGE - Defined in your projects .env file
# $DOCKERHUB_USERNAME
# $DOCKERHUB_PASSWORD
# $AWS_ACCESS_KEY_ID
# $AWS_ACCOUNT_ID
# $AWS_REGION
# $AWS_SECRET_ACCESS_KEY
# $AWS_ECR_REPOSITORY_NAME
# -------------------------------
# Terraform Deployment
# -------------------------------
# $TERRAFORM_REPO
# $TERRAFORM_SUBPATH
# $TERRAFORM_AWS_ACCESS_KEY_ID
# $TERRAFORM_AWS_SECRET_ACCESS_KEY
# $TERRAFORM_DOCKER_TAG_VAR
# $TERRAFORM_GIT_COMMIT_VAR
# $TERRAFORM_TARGET

image: atlassian/default-image:4

definitions:
  services:
    docker:
      memory: 3072

  steps:
    - step: &defaults
        name: Default Services & Caches For All Steps
        services:
          - docker
        artifacts:
          - .env.pipeline

    - step: &set-config
        <<: *defaults
        name: Calculate and set environment
        script:
          # Set DOCKER_REPOSITORY to the Docker image repository, extracted from the full image name
          - DOCKER_REPOSITORY=$(source .env && echo ${DOCKER_IMAGE#*/})

          # Set DOCKER_TAG to "latest-" followed by the current Git branch name, with any forward slashes replaced by dashes
          - DOCKER_TAG="latest-${BITBUCKET_BRANCH//\//-}"

          # Downcases the string stored in the DOCKER_TAG variable using the Bash parameter expansion syntax.
          - DOCKER_TAG="${DOCKER_TAG,,}"
          - echo "DOCKER_TAG=${DOCKER_TAG}" >> .env.pipeline

          # Set DOCKERHUB_ORGANISATION to the Docker Hub organization name, extracted from the full image name
          - DOCKERHUB_ORGANISATION=$(source .env && echo ${DOCKER_IMAGE%%/*})

          # Set DOCKERHUB_TAG to the Docker Hub image tag, constructed from the organization, repository, and tag name
          - echo "DOCKERHUB_TAG=${DOCKERHUB_ORGANISATION}/${DOCKER_REPOSITORY}:${DOCKER_TAG}" >> .env.pipeline

          # Set AWS_ECR_REGISTRY_URL to the AWS ECR registry URL based on the AWS account ID and region
          - echo "AWS_ECR_REGISTRY_URL=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com" >> .env.pipeline

          # Set AWS_ECR_TAG to the AWS ECR image tag, constructed from the registry URL, repository, and tag name
          - echo "AWS_ECR_TAG=$(source .env.pipeline && echo "${AWS_ECR_REGISTRY_URL}")/${AWS_ECR_REPOSITORY_NAME}:${DOCKER_TAG}" >> .env.pipeline

    - step: &build-test-push
        <<: *defaults
        name: Build, Test & Push Docker Image
        caches:
          - docker
        script:
          # Setup Environment Variables
          - source .env.pipeline

          # Build Docker Image
          - DOCKER_BUILDKIT=1 docker build -t ${DOCKER_TAG} --ssh default=$BITBUCKET_SSH_KEY_FILE .

          # Run Tests
          #- DOCKER_IMAGE=${DOCKER_TAG} docker-compose -f "docker-compose.yml" run --rm tests-without-volumes

          # Push To Dockerhub
          - echo "${DOCKERHUB_PASSWORD}" | docker login --username ${DOCKERHUB_USERNAME} --password-stdin
          - docker tag ${DOCKER_TAG} ${DOCKERHUB_TAG}
          - docker push ${DOCKERHUB_TAG}

          # Install AWS CLI
          - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          - unzip awscliv2.zip
          - ./aws/install

          # Push To ECR
          - aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ECR_REGISTRY_URL}
          - docker tag ${DOCKER_TAG} ${AWS_ECR_TAG}
          - docker push ${AWS_ECR_TAG}

    - step: &deploy
        <<: *defaults
        name: Deploy
        image: hashicorp/terraform:1.8
        script:
          # Setup Environment Variables
          - source .env.pipeline

          # Clone terraform scripts
          - git clone $TERRAFORM_REPO
          - cd $TERRAFORM_SUBPATH

          # Setup AWS Keys
          - export AWS_ACCESS_KEY_ID=$TERRAFORM_AWS_ACCESS_KEY_ID
          - export AWS_SECRET_ACCESS_KEY=$TERRAFORM_AWS_SECRET_ACCESS_KEY

          # Init Terraform
          - terraform init

          # Apply Terraform
          - >-
            terraform apply \
              --var "$TERRAFORM_DOCKER_TAG_VAR=$DOCKER_TAG" \
              --var "$TERRAFORM_GIT_COMMIT_VAR=$BITBUCKET_COMMIT" \
              --target $TERRAFORM_TARGET \
              -auto-approve

pipelines:
  branches:
    # Docker tag "latest"
    development:
      - step:
          <<: *set-config
          script:
            # Set DOCKER_REPOSITORY to the Docker image repository, extracted from the full image name
            - DOCKER_REPOSITORY=$(source .env && echo ${DOCKER_IMAGE#*/})

            # Set DOCKER_TAG to "latest"
            - DOCKER_TAG="latest"
            - echo "DOCKER_TAG=${DOCKER_TAG}" >> .env.pipeline

            # Set DOCKERHUB_ORGANISATION to the Docker Hub organization name, extracted from the full image name
            - DOCKERHUB_ORGANISATION=$(source .env && echo ${DOCKER_IMAGE%%/*})

            # Set DOCKERHUB_TAG to the Docker Hub image tag, constructed from the organization, repository, and tag name
            - echo "DOCKERHUB_TAG=${DOCKERHUB_ORGANISATION}/${DOCKER_REPOSITORY}:${DOCKER_TAG}" >> .env.pipeline

            # Set AWS_ECR_REGISTRY_URL to the AWS ECR registry URL based on the AWS account ID and region
            - echo "AWS_ECR_REGISTRY_URL=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com" >> .env.pipeline

            # Set AWS_ECR_TAG to the AWS ECR image tag, constructed from the registry URL, repository, and tag name
            - echo "AWS_ECR_TAG=$(source .env.pipeline && echo "${AWS_ECR_REGISTRY_URL}")/${AWS_ECR_REPOSITORY_NAME}:${DOCKER_TAG}" >> .env.pipeline

      - step: *build-test-push
      - step:
          <<: *deploy
          deployment: staging
          trigger: manual

    # Docker tag "latest-{branch_name}" i.e. "latest-dev_fixes"
    '{dev/*,feature/*,bugfix/*,hotfix/*,dev_*}':
      - step: *set-config
      - step: *build-test-push
      - step:
          <<: *deploy
          deployment: staging
          trigger: manual

  tags:
    # Docker tag "{semver}" i.e. "1.2.3"
    v*:
      - step:
          <<: *set-config
          script:
            # Set DOCKER_REPOSITORY to the Docker image repository, extracted from the full image name
            - DOCKER_REPOSITORY=$(source .env && echo ${DOCKER_IMAGE#*/})

            # Set DOCKER_TAG to "{semver}" i.e. "1.2.3"
            - DOCKER_TAG=$(echo $BITBUCKET_TAG | sed 's/v//g')
            - echo "DOCKER_TAG=${DOCKER_TAG}" >> .env.pipeline

            # Set DOCKERHUB_ORGANISATION to the Docker Hub organization name, extracted from the full image name
            - DOCKERHUB_ORGANISATION=$(source .env && echo ${DOCKER_IMAGE%%/*})

            # Set DOCKERHUB_TAG to the Docker Hub image tag, constructed from the organization, repository, and tag name
            - echo "DOCKERHUB_TAG=${DOCKERHUB_ORGANISATION}/${DOCKER_REPOSITORY}:${DOCKER_TAG}" >> .env.pipeline

            # Set AWS_ECR_REGISTRY_URL to the AWS ECR registry URL based on the AWS account ID and region
            - echo "AWS_ECR_REGISTRY_URL=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com" >> .env.pipeline

            # Set AWS_ECR_TAG to the AWS ECR image tag, constructed from the registry URL, repository, and tag name
            - echo "AWS_ECR_TAG=$(source .env.pipeline && echo "${AWS_ECR_REGISTRY_URL}")/${AWS_ECR_REPOSITORY_NAME}:${DOCKER_TAG}" >> .env.pipeline

      - step: *build-test-push
      - step:
          <<: *deploy
          deployment: staging
