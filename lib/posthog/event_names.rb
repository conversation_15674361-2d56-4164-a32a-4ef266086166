module Posthog
  EVENT_NAMES = {
    account_created: 'Account Created',
    deposit_completed: 'Deposit Completed',
    desposit_created: 'Deposit Created',
    deposit_failed: 'Deposit Failed',
    deposit_started: 'Deposit Started',
    investment_completed: 'Investment Completed',
    investment_created: 'Investment Created',
    investement_failed: 'Investment Failed',
    investment_started: 'Investment Started',
    kyc_light_complete: 'KYC Light Complete',
    kyc_light_failed: 'KYC Light Failed',
    kyc_light_started: 'KYC Light Started',
    kyc_regular_complete: 'KYC Regular Complete',
    kyc_regular_failed: 'KYC Regular Failed',
    kyc_regular_started: 'KYC Regular Started',
    new_password_requested: 'Password Reset Requested',
    new_password_set: 'New Password Set',
    registration_form_error: 'Registration Form Error',
    settings_changed: 'Settings Changed',
    signed_in: 'Signed In',
    signed_out: 'Signed Out',
    withdrawal_completed: 'Withdrawal Completed',
    withdrawal_failed: 'Withdrawal Failed',
    withdrawal_requested: 'Withdrawal Requested',
    withdrawal_started: 'Withdrawal Started'
}.freeze
end
