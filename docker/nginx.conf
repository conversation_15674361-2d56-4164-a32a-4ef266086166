server {
  listen 80;
  root /home/<USER>/webapp/public;
  passenger_user app;
  passenger_ruby /usr/bin/ruby3.3;
  passenger_min_instances 1;

  client_max_body_size 50M;
  server_tokens off;

  location / {
      passenger_enabled on;
  }

  location /assets/ {
       expires 1y;
  }
}

passenger_pool_idle_time 0;
passenger_pre_start http://localhost/;
passenger_show_version_in_header off;
passenger_preload_bundler on;


# Change passenger_ruby to new ruby version when doing upgrades.