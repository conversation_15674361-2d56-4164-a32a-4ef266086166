# This config file is required to pass environment variables from <PERSON><PERSON><PERSON> to <PERSON>.
# As otherwise <PERSON><PERSON><PERSON> does not pass any environment variables to child processes.
# Set Nginx config environment based on
# the values set in the .env file

env RAILS_ENV;
env DATABASE_NAME;
env DATABASE_USERNAME;
env DATABASE_PASSWORD;
env DATABASE_HOST;
env ERROR_ENVIRONMENT;
env SECRET_KEY_BASE;
env FRONTEND_URL;
env MANGOPAY_SANDBOX;
env MANGOPAY_CLIENT_ID;
env MANGOPAY_CLIENT_PASSPHRASE;
env S3_BUCKET;
env S3_ACCESS_KEY;
env S3_SECRET_KEY;
env S3_REGION;
env SITE_URL;
env REDIS_URL;
env LEGAL_USER_ID;
env EMAIL_ADMIN;
env EMAIL_FROM;
env EMAIL_DOMAIN;
env EMAIL_HOST;
env EMAIL_PORT;
env EMAIL_USERNAME;
env EMAIL_PASSWORD;
env POSTHOG_API_KEY;
env TELNYX_API_KEY;
env NUAPAY_API_KEY;
env NUAPAY_ENDPOINT;
env NUAPAY_WEBHOOK_SECRET;
env NUAPAY_PRIVATE_KEY;
env NUAPAY_KID;
env NUAPAY_CN;
env TWILIO_ACCOUNT_SID;
env TWILIO_AUTH_TOKEN;
env TWILIO_SERVICE_ID;
env FLG_BASE_URL;
env FLG_UPSERT_ENDPOINT;
env FLG_API_KEY;
env FLG_LEAD_GROUP_ID;
env FLG_SITE_ID;
env FLG_CSV_FILE_NAME;
env ENVIRONMENT;
env SHUFTI_CLIENT_ID;
env SHUFTI_SECRET_KEY;
env SHUFTI_BASE_URL;
env VOLT_ENDPOINT;
env VOLT_CLIENT_ID;
env VOLT_CLIENT_SECRET;
env VOLT_USERNAME;
env VOLT_PASSWORD;
env VOLT_CUSTOMER_ID;
env VOLT_NOTIFICATION_SECRET;