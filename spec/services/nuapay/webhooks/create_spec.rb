require 'rails_helper'

RSpec.describe NuaPay::Webhooks::Create, type: :service do
  let(:user_id) { 'user_123' }
  let(:log_id) { 'log_456' }
  let(:service) { described_class.new(user_id: user_id, log_id: log_id) }
  let(:encoded_api_key) { Base64.strict_encode64('nuapay_api_key') }


  describe '#initialize' do
    it 'assigns user_id and log_id' do
      expect(service.instance_variable_get(:@user_id)).to eq(user_id)
      expect(service.instance_variable_get(:@log_id)).to eq(log_id)
    end
  end

  describe '#sub_url' do
    it 'returns "webhooks"' do
      expect(service.send(:sub_url)).to eq('webhooks')
    end
  end

  describe '#req_body' do
    it 'returns the correct body' do
      expected_body = {
        name: "Open Banking Payment",
        targetUrl: "#{Rails.application.secrets.frontend_url}/payins/open_bankings/payment_complete?log_id=#{log_id}&user_id=#{user_id}",
        active: true,
        retryNumberOfDays: 1,
        signKey: Rails.application.secrets.nuapay_webhook_secret,
        eventTypes: [
          "PaymentReceived", "PaymentDeclined", "PaymentCompleted", "PaymentRejected"
        ]
      }
      expect(service.send(:req_body)).to eq(expected_body)
    end
  end

  describe '#call' do


    it 'sends a POST request with the correct URL and body' do
      client = instance_double(Faraday::Connection)
      allow(Faraday).to receive(:new).and_return(client)
      allow(client).to receive(:post).and_return(double('response', success?: true))

      expect(client).to receive(:post) do |&block|
        request = instance_double(Faraday::Request)
        allow(request).to receive(:url).and_return('webhooks')
        allow(request).to receive(:body=)
        block.call(request)
      end

      service.call
    end
  end
end
