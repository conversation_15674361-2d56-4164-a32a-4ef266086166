# spec/services/twilio/totp/create_spec.rb

require 'rails_helper'

RSpec.describe Twilio::Totp::Create do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:twilio_entity) do
    double('Twilio::Entity', new_factors: double('NewFactors', create: true)) do |args|
      expect(args[:friendly_name]).to eq(user.full_name)
      expect(args[:factor_type]).to eq('totp')
    end
  end
  let(:raise_mock_response) { double('Twilio::Response', status_code: 500, body: { 'message'=>'Error message' }) }


  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect { described_class.new(user: user, twilio_entity: twilio_entity) }.not_to raise_error
    end
  end

  describe '#call' do
    let(:service) { described_class.new(user: user, twilio_entity: twilio_entity) }
    let(:mock_factor) { double('Twilio::Factor', sid: '123-456-789') }

    before do
      allow(twilio_entity.new_factors).to receive(:create).with(friendly_name: user.full_name, factor_type: 'totp').and_return(mock_factor)
    end

    context 'when Twilio API call is successful' do
      it 'returns a success response with the factor data' do
        expect(service.call).to eq(success: true, data: mock_factor, message: '', status: :ok)
      end
    end

    context 'when Twilio API call raises an exception' do
      before do
        allow(twilio_entity.new_factors).to receive(:create).and_raise(Twilio::REST::RestError.new('Error message', raise_mock_response ))
      end

      it 'returns an error response with the correct error message' do
        expect(service.call).to eq(success: false, error: 'Error message', status: :unprocessable_entity)
      end
    end
  end
end
