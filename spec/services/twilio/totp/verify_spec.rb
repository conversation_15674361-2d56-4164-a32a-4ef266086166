# spec/services/twilio/totp/verify_spec.rb

require 'rails_helper'

RSpec.describe Twilio::Totp::Verify do
  let(:user_id) { 'user_id' }
  let(:factor_id) { 'factor_id' }
  let(:otp) { '123456' }
  let(:mock_entity) { double('Twilio::Entity') }
  let(:mock_factors) { double('Factors') }
  let(:raise_mock_response) { double('Twilio::Response', status_code: 500, body: { 'message'=>'Error message' }) }


  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect { described_class.new(user_id: user_id, factor_id: factor_id, otp: otp) }.not_to raise_error
    end
  end

  describe '#call' do
    let(:service) { described_class.new(user_id: user_id, factor_id: factor_id, otp: otp) }
    let(:mock_factor) { double('Twilio::Factor') }

    before do
      allow(mock_entity).to receive(:factors).and_return(mock_factors)
      allow(TwilioVerifyClient).to receive(:entities).and_return(mock_entity)
      allow(mock_factors).to receive(:update).and_return(mock_factor)
    end

    context 'when Twilio API call is successful' do
      it 'returns a success response with the factor data' do
        expect(service.call).to eq(success: true, data: mock_factor, status: :ok, message: '')
      end
    end

    context 'when Twilio API call raises an exception' do
      before do
        allow(mock_factors).to receive(:update).and_raise(Twilio::REST::RestError.new('Error message', raise_mock_response ))
      end

      it 'returns an error response with the correct error message' do
        expect(service.call).to eq(success: false, error: 'Error message', status: :unprocessable_entity)
      end
    end
  end

  describe '#entity_id' do
    let(:service) { described_class.new(user_id: user_id, factor_id: factor_id, otp: otp) }

    it 'returns the correct entity ID' do
      expect(service.send(:entity_id)).to eq("entity-#{user_id}")
    end
  end
end
