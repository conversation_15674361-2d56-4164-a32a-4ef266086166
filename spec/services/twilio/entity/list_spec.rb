# spec/services/twilio/entity/list_spec.rb

require 'rails_helper'
RSpec.describe Twilio::Entity::List do
  describe '#call' do
    let(:service) { described_class.new }
    let(:raise_mock_response) { double('Twilio::Response', status_code: 500, body: { 'message'=>'Error message' }) }

    context 'when Twilio API call is successful' do
      let(:mock_entities) { double('Twilio::Entities') }


      before do
        allow(TwilioVerifyClient.entities).to receive(:list).and_return(mock_entities)
      end

      it 'returns a success response with the entities data' do
        expect(service.call).to eq(success: true, data: mock_entities, message: '', status: :ok)
      end
    end

    context 'when Twilio API call raises an exception' do
      before do
        allow(TwilioVerifyClient.entities).to receive(:list).and_raise(Twilio::REST::RestError.new('Error message', raise_mock_response ))
      end

      it 'returns an error response with the correct error message' do
        expect(service.call).to eq(success: false, error: 'Error message', status: :unprocessable_entity)
      end
    end
  end
end
