# spec/services/twilio/entity/create_spec.rb

require 'rails_helper'

RSpec.describe Twilio::Entity::Create do
  let(:user_id) { 'user_id' }

  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect { described_class.new(user_id: user_id) }.not_to raise_error
    end
  end

  describe '#call' do
    let(:service) { described_class.new(user_id: user_id) }
    let(:mock_entity) { double('Twilio::Entity') }
    let(:raise_mock_response) { double('Twilio::Response', status_code: 500, body: { 'message'=>'Error message' }) }


    before do
      allow(TwilioVerifyClient.entities).to receive(:create).and_return(mock_entity)
    end

    it 'calls TwilioVerifyClient.entities.create with correct parameters' do
      expect(TwilioVerifyClient.entities).to receive(:create).with(identity: "entity-#{user_id}").once
      service.call
    end

    it 'returns a success response with entity data' do
      expect(service.call).to eq(success: true, data: mock_entity, status: :ok, message: '')
    end

    it 'returns an error response if an exception is raised' do
      allow(TwilioVerifyClient.entities).to receive(:create).and_raise(Twilio::REST::RestError.new('Error message', raise_mock_response ))
      expect(service.call).to eq(success: false, error: 'Error message', status: :unprocessable_entity)
    end
  end

  describe '#entity_id' do
    let(:service) { described_class.new(user_id: user_id) }

    it 'returns the correct entity ID' do
      expect(service.send(:entity_id)).to eq("entity-#{user_id}")
    end
  end
end
