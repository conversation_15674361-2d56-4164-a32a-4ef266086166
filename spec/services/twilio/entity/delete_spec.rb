# spec/services/twilio/entity/delete_spec.rb

require 'rails_helper'

RSpec.describe Twilio::Entity::Delete do
  let(:user_id) { 'user_id' }

  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect { described_class.new(user_id: user_id) }.not_to raise_error
    end
  end

  describe '#call' do
    let(:service) { described_class.new(user_id: user_id) }
    let(:mock_response) { double('Twilio::Response') }
    let(:raise_mock_response) { double('Twilio::Response', status_code: 500, body: { 'message'=>'Error message' }) }
    let(:mock_entity) { double('Twilio::Entity', delete: mock_response) }

    before do
      allow(TwilioVerifyClient).to receive(:entities).and_return(mock_entity)
    end

    context 'when Twilio API call is successful' do
      it 'returns a success response with the response data' do
        expect(service.call).to eq(success: true, data: mock_response, message: '', status: :ok)
      end
    end

    context 'when Twilio API call raises an exception' do
      before do
        allow(mock_entity).to receive(:delete).and_raise(Twilio::REST::RestError.new('Error message', raise_mock_response ))
      end

      it 'returns an error response with the correct error message' do
        expect(service.call).to eq(success: false, error: 'Error message',status: :unprocessable_entity,)
      end
    end
  end

  describe '#entity_id' do
    let(:service) { described_class.new(user_id: user_id) }

    it 'returns the correct entity ID' do
      expect(service.send(:entity_id)).to eq("entity-#{user_id}")
    end
  end
end
