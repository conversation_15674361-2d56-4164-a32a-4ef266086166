# spec/services/telnyx/templates/list_spec.rb

require 'rails_helper'

RSpec.describe Telnyx::Templates::List do
  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect { described_class.new }.not_to raise_error
    end
  end

  describe '#call' do
    let(:service) { described_class.new }

    before do
      allow(service).to receive(:telnyx_response)
    end

    it 'calls the Telnyx list templates API' do
      expect(service).to receive(:telnyx_response).once
      service.call
    end
  end

  describe '#url' do
    let(:service) { described_class.new }

    it 'returns the correct URL' do
      expect(service.send(:url)).to eq('verify_profiles/templates')
    end
  end
end
