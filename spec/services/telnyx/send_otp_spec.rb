# spec/services/telnyx/send_otp_spec.rb

require 'rails_helper'

RSpec.describe Telnyx::SendOtp do
  describe '#initialize' do
    let(:phone_number) { '+1234567890' }
    let(:profile_id) { 'profile_id' }

    it 'initializes with correct parameters' do
      expect { described_class.new(phone_number: phone_number, profile_id: profile_id) }.not_to raise_error
    end
  end

  describe '#call' do
    let(:phone_number) { '+1234567890' }
    let(:service) { described_class.new(phone_number: phone_number, profile_id: 'profile_id') }

    before do
      allow(service).to receive(:telnyx_response)
    end

    it 'calls the Telnyx send OTP API' do
      expect(service).to receive(:telnyx_response).once
      service.call
    end
  end

  describe '#req_body' do
    let(:phone_number) { '+1234567890' }
    let(:profile_id) { 'profile_id' }
    let(:service) { described_class.new(phone_number: phone_number, profile_id: profile_id) }

    it 'returns the correct request body' do
      expected_body = { phone_number: phone_number, verify_profile_id: profile_id }
      expect(service.send(:req_body)).to eq(expected_body)
    end
  end

  describe '#profile_selection' do
    let(:phone_number) { '+1234567890' }
    let(:service) { described_class.new(phone_number: phone_number) }

    before do
      allow_any_instance_of(Telnyx::Profiles::List).to receive(:call).and_return(
        instance_double('Telnyx::Response', success?: true, body: { 'data' => [{ 'id' => 'profile_id', 'sms' => { 'app_name' => 'Phone' }, 'name' => 'phone verification profile' }] }.to_json)
      )
    end

    it 'selects the correct profile for SMS app' do
      profile = service.send(:profile_selection)
      expect(profile).to eq('profile_id')
    end
  end
end
