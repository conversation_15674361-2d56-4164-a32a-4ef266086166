# spec/services/telnyx/verification_spec.rb

require 'rails_helper'

RSpec.describe Telnyx::Verification do
  let(:verification_id) { 'verification_id' }

  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect { described_class.new(verification_id) }.not_to raise_error
    end
  end

  describe '#call' do
    let(:service) { described_class.new(verification_id) }

    before do
      allow(service).to receive(:telnyx_response)
    end

    it 'calls the Telnyx verification API' do
      expect(service).to receive(:telnyx_response).once
      service.call
    end
  end

  describe '#url' do
    let(:service) { described_class.new(verification_id) }

    it 'returns the correct URL' do
      expect(service.send(:url)).to eq("verifications/#{verification_id}")
    end
  end
end
