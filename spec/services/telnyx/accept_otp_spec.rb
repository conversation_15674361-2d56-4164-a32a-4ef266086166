# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Telnyx::AcceptOtp, type: :service do
  let(:phone_number) { '1234567890' }
  let(:code) { '123456' }
  let(:profile_id) { 'profile_id_123' }
  let(:accept_otp_service) { described_class.new(phone_number: phone_number, code: code, profile_id: profile_id) }

  describe '#initialize' do
    it 'initializes with the correct parameters' do
      expect(accept_otp_service.instance_variable_get(:@phone_number)).to eq(phone_number)
      expect(accept_otp_service.instance_variable_get(:@code)).to eq(code.strip)
      expect(accept_otp_service.instance_variable_get(:@profile_id)).to eq(profile_id)
    end
  end

  describe '#url' do
    it 'generates the correct URL' do
      expect(accept_otp_service.send(:url)).to eq("verifications/by_phone_number/#{phone_number}/actions/verify")
    end
  end

  describe '#req_body' do
    context 'when profile_id is provided' do
      it 'includes the profile_id in the request body' do
        expect(accept_otp_service.send(:req_body)).to eq({ code: code, verify_profile_id: profile_id })
      end
    end

    context 'when profile_id is nil' do
      let(:accept_otp_service) { described_class.new(phone_number: phone_number, code: code) }

      it 'uses the profile_selection for verify_profile_id' do
        allow(Telnyx::Profiles::List).to receive(:new).and_return(double(call: double(success?: true, body: {data: [id: 'profile_id_123', name: 'phone verification profile']}.to_json)))
        expect(accept_otp_service.send(:req_body)).to eq({ code: code, verify_profile_id: 'profile_id_123' })
      end
    end
  end

  describe '#call' do
    it 'calls the telnyx_response method' do
      allow(accept_otp_service).to receive(:telnyx_response).and_return('some_response')

      expect(accept_otp_service.call).to eq('some_response')
    end
  end
end
