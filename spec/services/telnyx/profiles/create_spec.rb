# spec/services/telnyx/profiles/create_spec.rb
require 'rails_helper'

RSpec.describe Telnyx::Profiles::Create do
  let(:name) { 'Test Profile' }
  let(:destination_code) { 'GB' }

  describe '#initialize' do
    it 'initializes with correct parameters' do
      service = described_class.new(name: name, destination_code: destination_code)
      expect(service.instance_variable_get(:@name)).to eq(name)
      expect(service.instance_variable_get(:@destination_code)).to eq([destination_code])
    end
  end

  describe '#call' do
    let(:service) { described_class.new(name: name, destination_code: destination_code) }

    before do
      allow_any_instance_of(Telnyx::Templates::List).to receive(:call).and_return(
        instance_double('Telnyx::Response', success?: true, body: { 'data' => [{ 'id' => 'template_id' }] }.to_json)
      )
    end

    it 'calls the Telnyx templates list API' do
      expect_any_instance_of(Telnyx::Templates::List).to receive(:call).once
      service.call
    end

    it 'sets the correct template_id' do
      service.call
      expect(service.instance_variable_get(:@template_id)).to eq('template_id')
    end

    context 'when template list API call fails' do
      before do
        allow_any_instance_of(Telnyx::Templates::List).to receive(:call).and_return(
          instance_double('Telnyx::Response', success?: false, body: {}.to_json)
        )
      end

      it 'does not set template_id' do
        service.call
        expect(service.instance_variable_get(:@template_id)).to be_nil
      end
    end
  end

  describe '#req_body' do
    let(:service) { described_class.new(name: name, destination_code: destination_code) }

    it 'returns the correct request body' do
      expected_body = {
        name: name,
        sms: {
          messaging_template_id: nil, # Testing without setting template_id explicitly
          app_name: 'Phone',
          whitelisted_destinations: [destination_code]
        }
      }
      expect(service.send(:req_body)).to eq(expected_body)
    end
  end
end
