# spec/services/telnyx/profiles/delete_spec.rb
require 'rails_helper'
RSpec.describe Telnyx::Profiles::Delete do
  let(:profile_id) { 'profile_id' }

  describe '#initialize' do
    it 'initializes with correct parameters' do
      service = described_class.new(id: profile_id)
      expect(service.instance_variable_get(:@id)).to eq(profile_id)
    end
  end

  describe '#call' do
    let(:service) { described_class.new(id: profile_id) }

    before do
      allow(service).to receive(:telnyx_response)
    end

    it 'calls the Telnyx delete profile API' do
      expect(service).to receive(:telnyx_response).once
      service.call
    end
  end

  describe '#url' do
    let(:service) { described_class.new(id: profile_id) }

    it 'returns the correct URL' do
      expect(service.send(:url)).to eq("verify_profiles/#{profile_id}")
    end
  end
end