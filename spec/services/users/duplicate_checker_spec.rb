require 'rails_helper'

RSpec.describe Users::Duplicate<PERSON>hecker, type: :service do
  let(:first_name) { '<PERSON>' }
  let(:last_name) { 'Doe' }
  let(:dob) { '1990-01-01' }
  let(:post_code) { 'EH21 6UU' }
  let(:user_address) { create(:user_address, :personal, post_code: post_code) }
  let(:user) { user_address.user }
  let(:duplicate_checker) { described_class.new(first_name, last_name, dob, post_code) }

  describe '#initialize' do
    it 'strips the first name and last name' do
      checker = described_class.new("  <PERSON>  ", "  Do<PERSON>  ", dob, post_code)
      expect(checker.instance_variable_get(:@first_name)).to eq('John')
      expect(checker.instance_variable_get(:@last_name)).to eq('Doe')
    end

    it 'strips the post code' do
      checker = described_class.new(first_name, last_name, dob, " EH21 6UU  ")
      expect(checker.instance_variable_get(:@post_code)).to eq('EH21 6UU')
    end

    it 'assigns the dob correctly' do
      expect(duplicate_checker.instance_variable_get(:@dob)).to eq(dob)
    end
  end

  describe '#call' do
    before do
      user.update(first_name: first_name, last_name: last_name, date_of_birth: dob)
    end
    context 'when attributes are not blank' do
      it 'returns duplicate user' do
        result = duplicate_checker.call
        expect(result).to eq([user])
      end
    end

    context 'when any attribute is blank' do
      it 'returns nil' do
        checker_with_blank_first_name = described_class.new('', last_name, dob, post_code)
        checker_with_blank_last_name = described_class.new(first_name, '', dob, post_code)
        checker_with_blank_dob = described_class.new(first_name, last_name, '', post_code)
        checker_with_blank_post_code = described_class.new(first_name, last_name, dob, '')

        expect(checker_with_blank_first_name.call).to be_nil
        expect(checker_with_blank_last_name.call).to be_nil
        expect(checker_with_blank_dob.call).to be_nil
        expect(checker_with_blank_post_code.call).to be_nil
      end
    end
  end

  describe '#build_user_params' do
    it 'returns the correct user parameters' do
      expected_params = {
        first_name: first_name,
        last_name: last_name,
        date_of_birth: dob
      }
      expect(duplicate_checker.send(:build_user_params)).to eq(expected_params)
    end
  end

  describe '#build_user_address_params' do
    it 'returns the correct address parameters' do
      expected_params = { post_code: post_code }
      expect(duplicate_checker.send(:build_user_address_params)).to eq(expected_params)
    end
  end

  describe '#blank_attributes?' do
    context 'when any attribute is blank' do
      it 'returns true' do
        checker_with_blank_first_name = described_class.new('', last_name, dob, post_code)
        expect(checker_with_blank_first_name.send(:blank_attributes?)).to be_truthy
      end
    end

    context 'when all attributes are present' do
      it 'returns false' do
        expect(duplicate_checker.send(:blank_attributes?)).to be_falsey
      end
    end
  end
end
