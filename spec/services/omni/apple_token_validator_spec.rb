# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Omni::AppleTokenValidator, type: :service do
  let(:id_token) { 'apple_jwt_token' }
  let(:provider) { 'apple' }
  let(:validator) { described_class.new(id_token: id_token, provider: provider) }

  describe '#call' do
    context 'when the token is valid' do
      it 'returns true' do
        allow(Net::HTTP).to receive(:get).and_return({"keys":[{"kid":"apple_kid"}]}.to_json)

        allow(JWT).to receive(:decode).and_return([{},{ 'kid' => 'apple_kid' }])

        expect(validator.call).to be true
      end
    end

    context 'when the token is invalid' do
      it 'returns false if Apple keys cannot be fetched' do
        allow(Net::HTTP).to receive(:get).and_raise(StandardError)

        expect(validator.call).to be false
      end

      it 'returns false if no matching key is found' do
        allow(Net::HTTP).to receive(:get).and_return('{"keys":[{"kid":"different_kid"}]}')

        allow(JWT).to receive(:decode).and_return([{ 'kid' => 'apple_kid' }, {}])

        expect(validator.call).to be false
      end
    end

    context 'when there is an exception during JWT decoding' do
      it 'returns false if JWT decoding fails' do
        allow(Net::HTTP).to receive(:get).and_return('{"keys":[{"kid":"apple_kid"}]}')

        allow(JWT).to receive(:decode).and_raise(JWT::DecodeError)

        expect(validator.call).to be false
      end
    end
  end
end
