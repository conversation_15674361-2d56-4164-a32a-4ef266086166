require 'rails_helper'

RSpec.describe Omni::GoogleTokenValidator, type: :service do
  let(:token) { 'valid_token' }
  let(:provider) { 'google' }
  let(:uid) { '12345' }
  let(:service) { described_class.new(token: token, provider: provider, uid: uid) }

  describe '#initialize' do
    it 'initializes with token, provider, and uid' do
      expect(service.instance_variable_get(:@token)).to eq(token)
      expect(service.instance_variable_get(:@provider)).to eq(provider)
      expect(service.instance_variable_get(:@uid)).to eq(uid)
    end
  end

  describe '#call' do
    context 'when the token is valid and the uid matches' do
      it 'returns true' do
        allow(Faraday).to receive(:new).and_return(double(get: double(status: 200 , body: { 'id' => uid }.to_json)))

        result = service.call

        expect(result).to be true
      end
    end

    context 'when the token is valid but the uid does not match' do
      it 'returns false' do
        allow(Faraday).to receive(:new).and_return(double(get: double(status: 200 , body: { 'id' => 'wrong_uid' }.to_json)))

        result = service.call

        expect(result).to be false
      end
    end

    context 'when the token is invalid or the response status is not 200' do
      it 'returns false' do
        allow(Faraday).to receive(:new).and_return(double(get: double(status: 422 , body: { 'id' => 'wrong_uid' }.to_json)))

        result = service.call

        expect(result).to be nil
      end
    end

    context 'when an exception is raised during the request' do
      it 'returns false' do
        allow(Faraday).to receive(:new).and_raise(StandardError)

        result = service.call

        expect(result).to be false
      end
    end
  end
end
