require 'swagger_helper'

describe 'Certification Levels' do
  before do
    create_list(:certification_level, 5, :with_questions)
  end

  path '/api/v1/certifications/levels' do
    get 'Return an array of Certification::Level' do
      tags 'Certification Level'
      produces 'application/json'

      response '200', 'Success' do
        schema type: :array, items: {
          type: :object,
          properties: SwaggerSchema.certification_level_schema
        }

        run_test!
      end
    end
  end
end
