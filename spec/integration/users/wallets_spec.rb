require 'swagger_helper'

describe 'Wallets' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/{user_id}/wallets' do
    get 'Return an array of Mango::Wallet for a User' do
      tags 'Wallet'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      response '200', 'Success' do
        schema type: :array, items: {
          type: :object,
          properties: SwaggerSchema.wallet_schema
        }

        run_test!
      end
    end
  end
end
