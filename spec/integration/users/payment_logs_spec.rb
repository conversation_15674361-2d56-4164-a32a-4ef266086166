require 'swagger_helper'

describe 'Payment Logs' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  before do
    user.ensure_authentication_token!

    create(:payment_log, user: user, kind: 'BANK_WIRE')
    create(:payment_log, user: user, kind: 'CARD')
    create(:payment_log, user: user, kind: 'DIRECT_DEBIT')
  end

  path '/api/v1/users/{user_id}/payment_logs' do
    get 'Returns a list of PaymentLog for a User' do
      tags 'Payment Log'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      response '200', 'Success' do
        schema type: :array,
               items: {
                 type: :object,
                 properties: SwaggerSchema.payment_log_schema
               }

        run_test!
      end
    end
  end
end
