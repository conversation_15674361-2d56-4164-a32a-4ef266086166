require 'swagger_helper'

describe 'Sessions' do
  let(:password) { 'Passw0rd@1234' }
  let(:user) { create(:user_natural, password: password) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/sessions' do
    post 'Login a User account' do
      tags 'Sessions'
      consumes 'application/json'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          email: { type: :string, description: 'The email of the User', required: true },
          password: { type: :string, description: 'The password for the User', required: true }
        },
        required: ['email', 'password']
      }

      response '201', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.user_schema

        let(:payload) { { email: user.email, password: password } }

        run_test!
      end

      response '422', 'Failure' do
        let(:payload) { { email: user.email, password: 'incorrect' } }

        run_test!
      end
    end
  end

  path '/api/v1/users/sessions/{user_id}' do
    get 'Get the current User session' do
      tags 'Sessions'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.user_schema

        run_test!
      end

      response '401', 'Failure' do
        let(:Authorization) { "Token 123" }

        run_test!
      end
    end

    delete 'Destroy the current User session' do
      tags 'Sessions'
      security [authentication_token: {}]
      consumes 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      response '200', 'Success' do
        run_test!
      end

      response '401', 'Failure' do
        let(:Authorization) { "Token 123" }

        run_test!
      end
    end
  end
end
