require 'swagger_helper'

describe 'Potential Investment' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  let(:potential_investment) { create(:potential_investment, :with_items, user: user) }
  let(:potential_investment_id) { potential_investment.id }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/{user_id}/potential_investments' do
    post 'Create a PotentialInvestment' do
      tags 'Potential Investment'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          items_attributes: {
            type: :array,
            items: {
              type: :object,
              properties: {
                property_id: { type: :integer, description: 'The id of the Property' },
                quantity: { type: :integer, description: 'The quantity of shares to purchase/sell' }
              }
            }
          }
        }
      }

      response '201', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.potential_investment_schema

        let(:property) { create(:property_regular) }
        let(:potential_investment_params) { attributes_for(:potential_investment) }
        let(:item_params) { { property_id: property.id, quantity: 2 } }
        let(:payload) { potential_investment_params.merge(items_attributes: [item_params]) }

        run_test!
      end
    end
  end

  path '/api/v1/users/{user_id}/potential_investments/{potential_investment_id}' do
    get 'Returns a single PotentialInvestment' do
      tags 'Potential Investment'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :potential_investment_id, in: :path, type: :integer, description: 'The id of the User::PotentialInvestment'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.potential_investment_schema

        run_test!
      end

      response '404', 'Failure' do
        let(:potential_investment_id) { -1 }

        run_test!
      end
    end
  end

  path '/api/v1/users/{user_id}/potential_investments/{potential_investment_id}/invest' do
    put 'Turn a PotentialInvestment into ShareOrders' do
      tags 'Potential Investment'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :potential_investment_id, in: :path, type: :integer, description: 'The id of the User::PotentialInvestment'

      before do
        allow_any_instance_of(Mango::Wallet).to receive(:balance).and_return(potential_investment.total)
      end

      response '201', 'Success' do
        run_test!
      end

      response '404', 'Failure' do
        let(:potential_investment_id) { -1 }

        run_test!
      end
    end
  end
end
