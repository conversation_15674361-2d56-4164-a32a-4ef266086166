require 'swagger_helper'

describe 'Mandates' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }

  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  let(:mandate) { create(:mandate, user: user) }
  let(:mandate_id) { mandate.id }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/{user_id}/mandates' do
    get 'Returns a list of Mango::Mandate for a User' do
      tags 'Mandates'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      response '200', 'Success' do
        schema type: :array,
               items: {
                 type: :object,
                 properties: SwaggerSchema.mandate_schema
               }

        before do
          create_list(:mandate, 3, user: user)
        end

        run_test!
      end

      response '404', 'User not completed KYC' do
        let(:user) { create(:user_natural) }

        run_test!
      end
    end

    # @note: Temp disable this endpoint on <PERSON>'s request
    # post 'Create a Mango::Mandate for a User' do
    #   tags 'Mandates'
    #   security [authentication_token: {}]
    #   consumes 'application/json'
    #   parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
    #   parameter name: :payload, in: :body, schema: {
    #     type: :object,
    #     properties: {
    #       amount: { type: :integer, description: 'The payment amount' },
    #       bank_account_id: { type: :integer, description: 'The id of a Mango::BankAccount' },
    #       day: { type: :integer, description: 'The payment day' }
    #     },
    #     required: ['amount', 'bank_account_id', 'day']
    #   }
    #
    #   response '201', 'Success' do
    #     schema type: :object,
    #            properties: SwaggerSchema.mandate_schema
    #
    #     let(:payload) { { amount: 200, bank_account_id: 1, day: 1  } }
    #
    #     run_test!
    #   end
    #
    #   response '422', 'Failure' do
    #     schema type: :object,
    #            properties: {
    #              errors: {
    #               type: :object,
    #               properties: {
    #                 base: {
    #                   type: :array,
    #                   items: { type: :string }
    #                 }
    #               }
    #              }
    #            }
    #
    #     let(:payload) { { amount: nil } }
    #
    #     run_test!
    #   end
    #
    #   response '404', 'User not completed KYC' do
    #     let(:user) { create(:user_natural) }
    #     let(:payload) { { amount: 200, bank_account_id: 1, day: 1  } }
    #
    #     run_test!
    #   end
    # end
  end

  path '/api/v1/users/{user_id}/mandates/{mandate_id}' do
    get 'Fetch a single Mango::Mandate for a User' do
      tags 'Mandates'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :mandate_id, in: :path, type: :integer, description: 'The id of the Mango::Mandate'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.mandate_schema

        run_test!
      end

      response '404', 'Failure - Mandate not found' do
        let(:mandate_id) { '-1' }

        run_test!
      end
    end

    put 'Update a single Mango::Mandate for a User' do
      tags 'Mandates'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :mandate_id, in: :path, type: :integer, description: 'The id of the Mango::Mandate'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          amount: { type: :integer, description: 'The payment amount' },
          day: { type: :integer, description: 'The payment day' }
        },
        required: ['amount', 'day']
      }

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.mandate_schema

        let(:payload) { { amount: 200, day: 1  } }

        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties: {
                 errors: {
                  type: :object,
                  properties: {
                    base: {
                      type: :array,
                      items: { type: :string }
                    }
                  }
                 }
               }

        let(:payload) { { amount: 200, day: 60  } }

        run_test!
      end
    end
  end

  path '/api/v1/users/{user_id}/mandates/{mandate_id}/cancel' do
    delete 'Cancel a Mango::Mandate for a User' do
      tags 'Mandates'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :mandate_id, in: :path, type: :integer, description: 'The id of the Mango::Mandate'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.mandate_schema

        run_test!
      end
    end
  end
end
