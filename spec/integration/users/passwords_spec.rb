require 'swagger_helper'

describe 'Forgotten Password' do
  let(:password) { 'Passw0rd@1234' }
  let(:user) { create(:user_natural, password: password) }
  let(:user_id) { user.id }

  before do
    user.ensure_reset_token!
  end

  path '/api/v1/users/passwords' do
    post 'Request a password reset email for a User' do
      tags 'Forgotten Password'
      consumes 'application/json'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          email: { type: :string, description: 'The email of the User' },
        },
        required: ['email']
      }

      response '201', 'Success' do
        let(:payload) { { email: user.email } }

        run_test!
      end

      response '422', 'Failure' do
        let(:payload) { { email: '<EMAIL>' } }

        run_test!
      end
    end
  end

  path '/api/v1/users/passwords/{user_id}' do
    get 'Get a password reset User' do
      tags 'Forgotten Password'
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :reset_token, in: :query, type: :string, description: 'The reset_token of the User'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.user_schema

        let(:reset_token) { user.reset_token }

        run_test!
      end

      response '404', 'Failure' do
        let(:reset_token) { nil }

        run_test!
      end
    end

    put 'Update a User password from a reset_token' do
      tags 'Forgotten Password'
      consumes 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          password: { type: :string, description: 'A new password for the User' },
          reset_token: { type: :string, description: 'The reset_token of the User' },
        },
        required: ['password', 'reset_token']
      }

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.user_schema

        let(:payload) { { reset_token: user.reset_token, password: 'NewPassword!123' } }

        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties: SwaggerSchema.user_schema

        let(:payload) { { reset_token: user.reset_token, password: 'password' } }

        run_test!
      end

      response '404', 'Failure' do
        let(:payload) { { reset_token: nil, password: 'NewPassword!123' } }

        run_test!
      end
    end
  end
end
