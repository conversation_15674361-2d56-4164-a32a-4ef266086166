require 'swagger_helper'

describe 'Queued Actions' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  let(:potential_investment) { create(:potential_investment, user: user) }

  before do
    user.ensure_authentication_token!

    create(:user_queued_action, user: user, type: 'User::QueuedActionInvestment', target: potential_investment)
    create(:user_queued_action, user: user, type: 'User::QueuedActionPayout', amount: 200)
  end

  path '/api/v1/users/{user_id}/queued_actions' do
    get 'Returns a list of pending User::QueuedAction for a User' do
      tags 'Queued Actions'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      response '200', 'Success' do
        schema type: :array,
               items: {
                 type: :object,
                 properties: SwaggerSchema.queued_action_schema
               }

        run_test!
      end
    end
  end
end
