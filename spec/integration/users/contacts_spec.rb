require 'swagger_helper'

describe 'Contact' do
  let(:user) { create(:user_natural, :confirmed) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/{user_id}/contacts' do
    post 'Generic contact us method to send an email to UOWN admin' do
      tags 'Contact'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          email_body: { type: :string, description: 'The email body', required: true },
          email_subject: { type: :string, description: 'The email subject', required: true }
        },
        required: ['email_body', 'email_subject']
      }

      response '200', 'Success' do
        let(:payload) { { email_body: 'Test', email_subject: 'Test' } }

        run_test!
      end

      response '422', 'Fail' do
        let(:payload) { { email_body: nil, email_subject: 'Test' } }

        run_test!
      end
    end
  end
end
