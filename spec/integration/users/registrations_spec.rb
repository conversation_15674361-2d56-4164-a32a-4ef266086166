require 'swagger_helper'

describe 'Registrations' do
  let(:password) { 'Passw0rd@1234' }
  let(:user) { build(:user_natural, password: password) }

  path '/api/v1/users/registrations' do
    post 'Create a new User account' do
      tags 'Registrations'
      consumes 'application/json'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          email: { type: :string, description: 'The email address for the User', required: true },
          password: { type: :string, description: 'The password for the User', required: true },
          marketing: { type: :boolean, description: 'If the User consents to marking emails', required: true },
          type: { type: :string, description: 'The User type (Values: legal, natural) [defaults to natural]' }
        },
        required: ['email', 'password', 'marketing']
      }

      response '201', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.user_schema

        let(:payload) { { email: user.email, password: password, marketing: false } }

        run_test!
      end

      response '422', 'Failure' do
        let(:payload) { { email: user.email } }

        run_test!
      end
    end
  end
end
