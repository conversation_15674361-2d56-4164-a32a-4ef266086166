require 'swagger_helper'

describe 'Confirmation' do
  let(:user) { create(:user_natural) }
  let(:user_id) { user.id }

  before do
    user.ensure_confirmation_token!
  end

  path '/api/v1/users/confirmations/{user_id}' do
    put 'Confirm a User account from a confirmation_token' do
      tags 'Confirmation'
      consumes 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          confirmation_token: { type: :string, description: 'The confirmation_token of the User' }
        }
      }

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.user_schema

        let(:payload) { { confirmation_token: user.confirmation_token } }

        run_test!
      end

      response '404', 'Failure' do
        let(:payload) { { confirmation_token: nil } }

        run_test!
      end
    end
  end

  path '/api/v1/users/confirmations/resend' do
    let(:payload) { { email: user.email } }

    put 'Resend User confirmation_token' do
      tags 'Confirmation'
      consumes 'application/json'

      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          email: { type: :string, description: 'The email of the User' }
        }
      }

      response '200', 'Success' do
        run_test!
      end

      response '404', 'Failure' do
        let(:user) { create(:user_natural, :confirmed) }

        run_test!
      end
    end
  end
end
