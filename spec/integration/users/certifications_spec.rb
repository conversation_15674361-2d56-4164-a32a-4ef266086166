# require 'swagger_helper'
#
# describe 'Certifications' do
#   let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
#   let(:user_id) { user.id }
#   let(:Authorization) { "Token #{user.authentication_token}" }
#
#   let(:certification_level) { create(:certification_level) }
#   let(:payload) { { certification_level_id: certification_level.id } }
#
#   before do
#     user.ensure_authentication_token!
#   end
#
#   path '/api/v1/users/{user_id}/certifications' do
#     post 'Certify a User' do
#       tags 'Certifications'
#       security [authentication_token: {}]
#       consumes 'application/json'
#       parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
#       parameter name: :payload, in: :body, schema: {
#         type: :object,
#         properties: {
#           certification_level_id: { type: :integer, description: 'The id of the Certification::Level', required: true }
#         },
#         required: ['certification_level_id']
#       }
#
#       response '201', 'Success' do
#         schema type: :object,
#                properties: SwaggerSchema.user_schema,
#                required: []
#
#         run_test!
#       end
#
#       response '422', 'Failure' do
#         schema type: :object,
#                properties:  SwaggerSchema.error_schema
#
#         let(:payload) { { certification_level_id: nil } }
#
#         run_test!
#       end
#     end
#   end
#
#   path '/api/v1/users/{user_id}/certifications/failed' do
#     put 'Log a failed certification attempt for a User' do
#       tags 'Certifications'
#       security [authentication_token: {}]
#       consumes 'application/json'
#       parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
#       parameter name: :payload, in: :body, schema: {
#         type: :object,
#         properties: {
#           certification_level_id: { type: :integer, description: 'The id of the Certification::Level', required: true }
#         },
#         required: ['certification_level_id']
#       }
#
#       response '201', 'Success' do
#         schema type: :object,
#                properties: SwaggerSchema.user_schema,
#                required: []
#
#         run_test!
#       end
#     end
#   end
# end
