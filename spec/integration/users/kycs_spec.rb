require 'swagger_helper'

describe 'KYC' do
  let(:user) { create(:user_natural, :confirmed) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  let(:mandate) { create(:mandate, user: user) }
  let(:mandate_id) { mandate.id }

  before do
    user.ensure_authentication_token!
    allow_any_instance_of(::FlgCrm::Users).to receive(:sync_to_crm).and_return(OpenStruct.new(success?: true, errors: ''))
  end

  path '/api/v1/users/{user_id}/kycs' do
    post 'KYC a User' do
      tags 'KYC'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          level: { type: :string, description: 'The KYC level required (light|regular)' },
          # Light
          title: { type: :string, description: 'The User title (i.e. Mr)' },
          middle_name: { type: :string, description: 'The User middle name' },
          date_of_birth: { type: :string, description: 'The User date of birth' },
          phone_number: { type: :string, description: 'The User phone number' },
          call_me: { type: :boolean, description: 'Accept marketing information' },
          nationality: { type: :string, description: 'The User nationality (ISO 3166-1 alpha-2)' },
          country_of_residence: { type: :string, description: 'The User country of residence (ISO 3166-1 alpha-2)' },
          legal_type: { type: :string, description: 'The User legal type (Values: BUSINESS, ORGANIZATION, SOLETRADER) [Legal User only]' },
          employment_status: { type: :string, description: 'The User employment status' },
          experience: { type: :string, description: 'How much investment experience the User has' },
          planned_investment: { type: :string, description: 'How much the user is planning on investing' },
          address_attributes: { type: :object, description: 'The users address details', properties: {
            address_1: { type: :string, description: 'The first line of the User address' },
            address_2: { type: :string, description: 'The second line of the User address' },
            region: { type: :string, description: 'The User address region' },
            city: { type: :string, description: 'The User address city' },
            country: { type: :string, description: 'The User address country (ISO 3166-1 alpha-2)' },
            post_code: { type: :string, description: 'The User address post code' }
          } },
          # Regular
          income_range: { type: :string, description: 'The User income range [Natural User only]' },
          occupation: { type: :string, description: 'The User occupation [Natural User only]' },
          identity_proof: { type: :string, description: 'Base64 Encoded PDF document' },
          articles_of_association: { type: :string, description: 'Base64 Encoded PDF document [Legal User only]' },
          registration_proof: { type: :string, description: 'Base64 Encoded PDF document [Legal User only]' },
          shareholder_declaration: { type: :string, description: 'Base64 Encoded PDF document [Legal User only]' },
          headquarters_attributes: { type: :object, description: 'The users address details', properties: {
            address_1: { type: :string, description: 'The headquarters address [Legal User only]' },
            address_2: { type: :string, description: 'The headquarters address [Legal User only]' },
            city: { type: :string, description: 'The headquarters address [Legal User only]' },
            region: { type: :string, description: 'The headquarters address [Legal User only]' },
            post_code: { type: :string, description: 'The headquarters address [Legal User only]' },
            country: { type: :string, description: 'The headquarters address [Legal User only]' },
          } }
        }
      }

      response '201', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.user_schema

        let(:payload) { attributes_for(:user_natural, :confirmed, :kyc_light_complete).merge(level: 'light', address_attributes: attributes_for(:user_address, kind: 'personal')) }

        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties:  SwaggerSchema.error_schema

        let(:payload) { { amount: nil } }

        run_test!
      end
    end

    path '/api/v1/users/{user_id}/kycs/check' do
      post 'Check if a User will go over their KYC limit' do
        tags 'KYC'
        security [authentication_token: {}]
        consumes 'application/json'
        parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
        parameter name: :payload, in: :body, schema: {
          type: :object,
          properties: {
            credit: { type: :integer, description: 'An additional debit amount' }
          }
        }

        response '200', "The User won't go over their limit" do
          let(:payload) { { credit: 10, debit: 10 } }

          run_test!
        end

        response '422', 'The User will go over their limit' do
          let(:payload) { { credit: (User::MAX_KYC_CREDIT + 1) } }

          run_test!
        end
      end
    end
  end
end
