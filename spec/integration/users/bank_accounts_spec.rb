require 'swagger_helper'

describe 'Bank Accounts' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  before do
    user.ensure_authentication_token!
    allow(Mango::BankAccount).to receive(:allow_new_account?).with(user).and_return(true)
  end

  path '/api/v1/users/{user_id}/bank_accounts' do
    get 'Returns a list of bank accounts for the user' do
      tags 'Bank Accounts'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      response '200', 'Success' do
        schema type: :array,
               items: {
                 type: :object,
                 properties: SwaggerSchema.bank_account_schema
               }

        run_test!
      end

      response '404', 'User not completed KYC' do
        let(:user) { create(:user_natural) }

        run_test!
      end
    end

    before do
      allow(Mango::BankAccount).to receive(:allow_new_account?).with(user).and_return(true)
    end
    post 'Create a bank account' do
      tags 'Bank Accounts'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          account_number: { type: :integer, description: 'The bank accounts account number' },
          address_1: { type: :integer, description: 'The registered address of the card' },
          address_2: { type: :integer, description: 'The registered address of the card' },
          city: { type: :integer, description: 'The registered address of the card' },
          country: { type: :integer, description: 'The registered address of the card' },
          post_code: { type: :integer, description: 'The registered address of the card' },
          region: { type: :integer, description: 'The registered address of the card' },
          sort_code: { type: :integer, description: 'The bank accounts sort code' }
        },
        required: ['account_number', 'address_1', 'address_2', 'city', 'country', 'post_code', 'region', 'sort_code']
      }

      response '201', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.bank_account_schema

        let(:payload) { attributes_for(:mango_bank_account) }

        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties: {
                 errors: {
                  type: :object,
                  properties: {
                    base: {
                      type: :array,
                      items: { type: :string }
                    }
                  }
                 }
               }

        let(:payload) { { account_number: nil } }

        run_test!
      end

      response '404', 'User not completed KYC' do
        let(:user) { create(:user_natural) }
        let(:payload) { attributes_for(:mango_bank_account) }

        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties: SwaggerSchema.bank_account_schema

        let(:payload) { attributes_for(:mango_bank_account) }

        before do
          allow(Mango::BankAccount).to receive(:allow_new_account?).with(user).and_return(false)
        end

        run_test! do |response|
          expect(JSON.parse(response.body)).to eq ({"errors"=>{"base"=>["You are not authorized to perform this action."]}})
        end
      end
    end
  end

  path '/api/v1/users/{user_id}/bank_accounts/{bank_account_id}' do
    get 'Returns a single bank account for the user' do
      tags 'Bank Accounts'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :bank_account_id, in: :path, type: :integer, description: 'The id of the Mango::BankAccount'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.bank_account_schema

        # Bank accounts 1 & 2 are defined in the mocks
        let(:bank_account_id) { 1 }

        run_test!
      end
    end
  end

  path '/api/v1/users/{user_id}/bank_accounts/{bank_account_id}/deactivate' do
    delete 'Deactivate a single bank account for the user and any mandates relating to it' do
      tags 'Bank Accounts'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :bank_account_id, in: :path, type: :integer, description: 'The id of the Mango::BankAccount'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.bank_account_schema

        # Bank accounts 1 & 2 are defined in the mocks
        let(:bank_account_id) { 1 }

        run_test!
      end
    end
  end
end
