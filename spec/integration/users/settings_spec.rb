require 'swagger_helper'

describe 'Change Password' do
  let(:password) { 'Passw0rd@1234' }
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, password: password) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }
  let(:payload) { { old_password: password, password: 'Qwerty123@!', password_confirmation: 'Qwerty123@!' } }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/{user_id}/settings/update_password' do
    post 'Change a User password' do
      tags 'Change Password'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          old_password: { type: :string, description: 'The old_password of the User', required: true },
          password: { type: :string, description: 'The new password for the User', required: true },
          password_confirmation: { type: :string, description: 'The password confirmation for the User', required: true },
        },
        required: ['old_password', 'password', 'password_confirmation']
      }

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.user_schema,
               required: []

        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties:  SwaggerSchema.error_schema

        let(:payload) { { new_password: 'Qwerty123@!' } }

        run_test!
      end
    end
  end
end
