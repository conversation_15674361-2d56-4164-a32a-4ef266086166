require 'swagger_helper'

describe 'Dashboard' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/{user_id}/dashboard' do
    get 'Returns a Dashboard (summary of investments) for a User' do
      tags 'Dashboard'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :kind, in: :query, type: :string, description: 'Defaults to Properties/Developments (Values: cause)'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.dashboard_schema

        let(:kind) { nil }

        run_test!
      end

      response '200', 'Success (Causes)' do
        schema type: :object,
               properties: SwaggerSchema.dashboard_schema

        let(:kind) { 'cause' }

        run_test!
      end
    end
  end
end
