require 'swagger_helper'

describe 'Tax Statements' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }
  let(:property) { create(:property_regular) }

  before do
    user.ensure_authentication_token!

    create(:share_log, user: user, property: property, created_at: 2.months.ago, quantity: 10)
    create(:share_log, user: user, property: property, created_at: 1.month.ago, quantity: -10)
  end

  path '/api/v1/users/{user_id}/tax_statement' do
    get 'Return a User::TaxStatement for a User' do
      tags 'Tax Statement'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :start_date, in: :query, type: :string, description: 'Start date for filtering (Format: YYYY/MM/DD)'
      parameter name: :end_date, in: :query, type: :string, description: 'End date for filtering (Format: YYYY/MM/DD)'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.tax_statement_schema

        let(:start_date) { Date.today - 1.year }
        let(:end_date) { Date.today }

        run_test!
      end

      response '422', 'Failure - Invalid Dates Passed' do
        let(:start_date) { Date.today + 1.year }
        let(:end_date) { Date.today }

        run_test!
      end
    end
  end
end
