require 'swagger_helper'

describe 'Share Orders' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  let(:share_order) { create(:share_buy_order, user: user) }
  let(:share_order_id) { share_order.id }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/{user_id}/shares/orders' do
    get 'Get a list of pending Share::Order for a User' do
      tags 'Share Order'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      response '200', 'Success' do
        schema type: :array, items: {
          type: :object,
          properties: SwaggerSchema.share_order_schema
        }

        before do
          create_list(:share_buy_order, 2, user: user)
        end

        run_test!
      end
    end
  end

  path '/api/v1/users/{user_id}/shares/orders/new' do
    get 'Returns a new unsaved Share::Order' do
      tags 'Share Order'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.share_order_schema

        run_test!
      end
    end
  end

  path '/api/v1/users/{user_id}/shares/orders' do
    post 'Create a new Share::Order for a User' do
      tags 'Share Order'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          kind: { type: :integer, description: 'The kind of Share::Order (Values: buy, sell, easy_exit)' },
          property_id: { type: :integer, description: 'The id of the Property' },
          quantity: { type: :integer, description: 'The quantity of shares to purchase/sell' }
        },
        required: ['kind', 'property_id', 'quantity']
      }

      response '201', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.share_order_schema

        let(:property) { create(:property_regular) }
        let(:payload) { attributes_for(:share_buy_order, property_id: property.id) }

        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties: {
                 errors: {
                  type: :object,
                  properties: {
                    base: {
                      type: :array,
                      items: { type: :string }
                    }
                  }
                 }
               }

        let(:payload) { { kind: 'buy' } }

        run_test!
      end
    end
  end

  path '/api/v1/users/{user_id}/shares/orders/{share_order_id}' do
    get 'Get an individual Share::Order for a User' do
      tags 'Share Order'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :share_order_id, in: :path, type: :integer, description: 'The id of the Share::Order'

      response '200', 'Success' do
        schema type: :object,
               properties: SwaggerSchema.share_order_schema

        run_test!
      end

      response '404', 'Failure' do
        let(:share_order_id) { -1 }

        run_test!
      end
    end
  end

  path '/api/v1/users/{user_id}/shares/orders/{share_order_id}/cancel' do
    delete 'Queues a Share::Order for cancellation' do
      tags 'Share Order'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :share_order_id, in: :path, type: :integer, description: 'The id of the Share::Order'

      response '200', 'Success' do
        let(:share_log) { create(:share_log, user: user, quantity: 5) }
        let(:share_order) { create(:share_sell_order, user: user, property: share_log.property, quantity: share_log.quantity) }

        run_test!
      end

      response '422', 'Failure' do
        let(:share_order_id) { -1 }

        run_test!
      end
    end
  end
end
