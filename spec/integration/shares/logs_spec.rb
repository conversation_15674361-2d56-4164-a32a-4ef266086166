require 'swagger_helper'

describe 'Share Logs' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  before do
    user.ensure_authentication_token!

    create_list(:share_log, 3, user: user)
  end

  path '/api/v1/users/{user_id}/shares/logs' do
    get 'Return an array of Share::Log for a User' do
      tags 'Share Log'
      security [authentication_token: {}]
      produces 'application/json'

      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :start_date, in: :query, type: :string, description: 'Start date for filtering (Format: YYYY/MM/DD)'
      parameter name: :end_date, in: :query, type: :string, description: 'End date for filtering (Format: YYYY/MM/DD)'

      response '200', 'Success' do
        schema type: :array, items: {
          type: :object,
          properties: SwaggerSchema.share_log_schema
        }

        let(:start_date) { nil }
        let(:end_date) { nil }

        run_test!
      end

      response '422', 'Failure - Invalid Dates Passed' do
        let(:start_date) { Date.today + 1.year }
        let(:end_date) { Date.today }

        run_test!
      end
    end
  end
end
