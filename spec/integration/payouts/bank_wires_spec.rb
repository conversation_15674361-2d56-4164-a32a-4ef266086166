require 'swagger_helper'

describe 'Payout Bank Wire' do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }
  let(:payload) { { amount: 123, bank_account_id: 1 } }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/{user_id}/payouts/bank_wires' do
    post 'Create a Mango::PayOut / User::QueuedActionPayout' do
      tags 'Bank Wire'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          amount: { type: :integer },
          bank_account_id: { type: :integer }
        },
        required: ['amount', 'bank_account_id']
      }

      response '200', 'Success' do
        schema type: :object,
               properties: {
                 id: { type: :string },
                 amount: { type: :integer },
                 bank_account_id: { type: :string },
                 status: { type: :string }
               }

        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties:  SwaggerSchema.error_schema

        let(:payload) { { amount: nil } }

        run_test!
      end

      response '404', 'User not KYC regular or pending' do
        let(:user) { create(:user_natural) }

        run_test!
      end
    end
  end
end
