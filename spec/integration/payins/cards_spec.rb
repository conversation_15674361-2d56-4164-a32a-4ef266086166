require 'swagger_helper'

describe 'Payin Card' do
  let(:user) { create(:user_natural, :confirmed,:kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:potential_investment) { create(:potential_investment, user: user) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/{user_id}/payins/cards' do
    post 'Create a MangoPay::CardRegistration' do
      tags 'Card Payment'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'

      response '201', 'Success' do
        schema type: :object,
               properties: {
                 id: { type: :string },
                 access_key: { type: :string },
                 card_registration_url: { type: :string },
                 pre_registration_data: { type: :string }
               }

        run_test!
      end

      response '404', 'User not completed KYC' do
        let(:user) { create(:user_natural) }

        run_test!
      end
    end
  end

  path '/api/v1/users/{user_id}/payins/cards/{id}' do
    put 'Create a MangoPay::PayIn::Card::Direct' do
      tags 'Card Payment'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :id, in: :path, type: :integer, description: 'The card id returned by the mangopay card registration js library (https://github.com/Mangopay/cardregistration-js-kit)'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          amount: { type: :integer },
          potential_investment_id: { type: :integer },
          browser_info: {
            type: :object,
            properties: {
              accept_header: { type: :string },
              color_depth: { type: :string },
              ip_address: { type: :string },
              java_enabled: { type: :string },
              javascript_enabled: { type: :string },
              language: { type: :string },
              screen_height: { type: :string },
              screen_width: { type: :string },
              time_zone_offset: { type: :string },
              user_agent: { type: :string }
            }
          }
        },
        required: ['amount']
      }

      let(:payload) { { amount: 123, potential_investment_id: potential_investment.id } }
      let(:id) { 'valid_card_id' }

      response '200', 'Success' do
        run_test!
      end

      response '307', '3D Secure Redirect' do
        let(:id) { '3d_secure_card_id' }

        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties: SwaggerSchema.error_schema

        let(:payload) { { amount: nil } }
        let(:id) { 'invalid_card_id' }

        run_test!
      end

      response '404', 'User not completed KYC' do
        let(:user) { create(:user_natural) }

        run_test!
      end
    end
  end

  path '/api/v1/users/{user_id}/payins/cards/confirm' do
    put 'Confirm a MangoPay::PayIn::Card::Direct' do
      tags 'Card Payment'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          transaction_id: { type: :integer },
          potential_investment_id: { type: :integer }
        },
        required: ['transaction_id']
      }

      let(:payload) { { transaction_id: 'valid_transaction_id', potential_investment_id: potential_investment.id } }

      response '200', 'Success' do
        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties: {
                 errors: {
                  type: :object,
                  properties: {
                    base: {
                      type: :array,
                      items: { type: :string }
                    }
                  }
                 }
               }

        let(:payload) { { transaction_id: 'invalid_transaction_id' } }

        run_test!
      end
    end
  end
end
