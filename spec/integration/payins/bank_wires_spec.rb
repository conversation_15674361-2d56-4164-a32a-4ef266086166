require 'swagger_helper'

describe 'Payin Bank Wire' do
  let(:user) { create(:user_natural, :confirmed,:kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:potential_investment) { create(:potential_investment, user: user) }
  let(:user_id) { user.id }
  let(:Authorization) { "Token #{user.authentication_token}" }
  let(:payload) { { amount: 123, potential_investment_id: potential_investment.id } }

  before do
    user.ensure_authentication_token!
  end

  path '/api/v1/users/{user_id}/payins/bank_wires' do
    post 'Create a MangoPay::Payin::BankWire' do
      tags 'Bank Wire'
      security [authentication_token: {}]
      consumes 'application/json'
      parameter name: :user_id, in: :path, type: :integer, description: 'The id of the User'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          amount: { type: :integer, description: 'The amount in pence', required: true },
          potential_investment_id: { type: :integer, description: 'The id of a PotentialInvestment to complete on success' }
        },
        required: ['amount']
      }

      response '200', 'Success' do
        schema type: :object,
               properties: {
                 id: { type: :string },
                 amount: { type: :integer },
                 bic: { type: :string },
                 fees: { type: :integer },
                 iban: { type: :string },
                 owner_name: { type: :string },
                 reference: { type: :string },
                 type: { type: :string }
               }

        run_test!
      end

      response '422', 'Failure' do
        schema type: :object,
               properties:  SwaggerSchema.error_schema

        let(:payload) { { amount: nil } }

        run_test!
      end

      response '404', 'User not completed KYC' do
        let(:user) { create(:user_natural) }

        run_test!
      end
    end
  end
end
