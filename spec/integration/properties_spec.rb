require 'swagger_helper'

describe 'Properties' do
  before do
    create_list(:property_regular, 2, :with_photos, :with_floorplans, :with_documents, :with_news_items, :with_tags)
    create_list(:property_development, 2, :with_photos, :with_floorplans, :with_documents, :with_news_items, :with_tags)
  end

  let(:property) { create(:property_regular, :with_photos, :with_floorplans, :with_documents, :with_news_items, :with_tags) }

  path '/api/v1/properties' do
    get 'Return an array of Property::Regular' do
      tags 'Property'
      produces 'application/json'

      response '200', 'Success' do
        schema type: :array, items: {
          type: :object,
          properties: SwaggerSchema.property_schema
        }

        run_test!
      end
    end
  end

  path '/api/v1/properties/{id}' do
    get 'Return a single Property::Regular by id' do
      tags 'Property'
      produces 'application/json'

      parameter name: :id, in: :path, type: :integer, description: 'The id of the Property::Regular'

      response '200', 'Success' do
        schema type: :object, properties: SwaggerSchema.property_schema

        let(:id) { property.id }

        run_test!
      end
    end
  end

  path '/api/v1/properties/by_slug' do
    get 'Return a single Property::Regular by slug' do
      tags 'Property'
      produces 'application/json'

      parameter name: :slug, in: :query, type: :string, description: 'The slug of the Property::Regular'

      response '200', 'Success' do
        schema type: :object, properties: SwaggerSchema.property_schema

        let(:slug) { property.slug }

        run_test!
      end
    end
  end
end
