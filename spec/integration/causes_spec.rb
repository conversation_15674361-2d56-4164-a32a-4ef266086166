require 'swagger_helper'

describe 'Causes' do
  before do
    create_list(:property_cause, 2, :with_photos, :with_documents, :with_news_items, :with_tags)
  end

  let(:cause) { create(:property_cause, :with_photos, :with_documents, :with_news_items, :with_tags) }

  path '/api/v1/causes' do
    get 'Return an array of Property::Cause' do
      tags 'Cause'
      produces 'application/json'

      response '200', 'Success' do
        schema type: :array, items: {
          type: :object,
          properties: SwaggerSchema.cause_schema
        }

        run_test!
      end
    end
  end

  path '/api/v1/causes/{id}' do
    get 'Return a single Property::Cause by id' do
      tags 'Cause'
      produces 'application/json'

      parameter name: :id, in: :path, type: :integer, description: 'The id of the Propery::Cause'

      response '200', 'Success' do
        schema type: :object, properties: SwaggerSchema.cause_schema

        let(:id) { cause.id }

        run_test!
      end
    end
  end

  path '/api/v1/causes/by_slug' do
    get 'Return a single Property::Cause by slug' do
      tags 'Cause'
      produces 'application/json'

      parameter name: :slug, in: :query, type: :string, description: 'The slug of the Property::Cause'

      response '200', 'Success' do
        schema type: :object, properties: SwaggerSchema.cause_schema

        let(:slug) { cause.slug }

        run_test!
      end
    end
  end
end
