require 'rails_helper'

RSpec.describe Property::CauseSerializer do
  let(:cause) { create(:property_cause) }
  let(:serializer)  { described_class.new(cause) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  subject { JSON.parse(serialization.to_json) }

  it 'includes the expected attributes' do
    expect(subject.keys).to contain_exactly('id',
                                            'available_shares',
                                            'certification_level_ids',
                                            'created_at',
                                            'description',
                                            'funded',
                                            'name',
                                            'placeholder',
                                            'share_count',
                                            'share_price',
                                            'slug',
                                            'target_amount',
                                            'thumbnail_label',
                                            'type',
                                            'unique_contributors',
                                            'documents',
                                            'news_items',
                                            'photos',
                                            'tags',
                                            'address_1',
                                            'city',
                                            'postcode')
  end
end
