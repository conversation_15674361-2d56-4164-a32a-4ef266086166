# frozen_string_literal: true
require 'rails_helper'

RSpec.describe Property::LegalDocumentSerializer, type: :serializer do
  let(:property) { create(:property) }
  let(:legal_document) { property.legal_documents.create(title: 'legal_document_title') }
  let(:serializer) { described_class.new(legal_document) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  describe "attributes" do
    subject { JSON.parse(serialization.to_json) }

    it "includes the expected attributes" do
      expect(subject.keys).to match_array(%w[id attachment_url title])
    end

    context "when attachment is present" do
      before do
        legal_document.attachment.attach(
          io: File.open(Rails.root.join("spec", "fixtures", "files", "document.pdf")),
          filename: "document.pdf",
          content_type: "application/pdf"
        )
      end

      it "includes the attachment URL" do
        expect(subject["attachment_url"]).to be_present
        expect(subject["attachment_url"]).to include("document.pdf")
      end
    end

    context "when attachment is not present" do
      it "does not include the attachment URL" do
        expect(subject["attachment_url"]).to be_nil
      end
    end
  end
end
