require 'rails_helper'

RSpec.describe Property::NewsItemSerializer do
  let(:news_item) { create(:property_news_item) }
  let(:serializer)  { described_class.new(news_item) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  subject { JSON.parse(serialization.to_json) }

  it 'includes the expected attributes' do
    expect(subject.keys).to contain_exactly('id',
                                            'content',
                                            'date',
                                            'title')
  end
end
