require 'rails_helper'

RSpec.describe Property::LoanSerializer do
  let(:property) { create(:property_development) }
  let(:serializer)  { described_class.new(property) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  subject { JSON.parse(serialization.to_json) }

  it 'includes the expected attributes' do
    expect(subject.keys).to contain_exactly('id',
                                            'address_1',
                                            'address_2',
                                            'annualised_return',
                                            'available_shares',
                                            'certification_level_ids',
                                            'city',
                                            'created_at',
                                            'description',
                                            'estimated_completion_date',
                                            'finance',
                                            'finance_label',
                                            'funded',
                                            'gdv',
                                            'investment_case_and_risk',
                                            'name',
                                            'placeholder',
                                            'postcode',
                                            'profit',
                                            'property_amount',
                                            'property_amount_label',
                                            'property_fee_legal_and_professional',
                                            'property_fee_legal_and_professional_label',
                                            'share_count',
                                            'share_price',
                                            'site_value',
                                            'site_value_label',
                                            'slug',
                                            'spv_name',
                                            'target_amount',
                                            'term',
                                            'thumbnail_label',
                                            'type',
                                            'documents',
                                            'floorplans',
                                            'legal_documents',
                                            'news_items',
                                            'photos',
                                            'tags')
  end
end
