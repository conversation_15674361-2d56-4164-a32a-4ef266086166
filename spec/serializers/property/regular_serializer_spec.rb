require 'rails_helper'

RSpec.describe Property::RegularSerializer do
  let(:property) { create(:property_regular) }
  let(:serializer)  { described_class.new(property) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  subject { JSON.parse(serialization.to_json) }

  it 'includes the expected attributes' do
    expect(subject.keys).to contain_exactly('id',
                                            'address_1',
                                            'address_2',
                                            'available_shares',
                                            'certification_level_ids',
                                            'city',
                                            'created_at',
                                            'description',
                                            'easy_exit',
                                            'funded',
                                            'guaranteed_yield',
                                            'hpi',
                                            'hpi_area',
                                            'investment_case_and_risk',
                                            'name',
                                            'placeholder',
                                            'postcode',
                                            'property_amount',
                                            'property_fee_deferred_tax',
                                            'property_fee_legal_and_professional',
                                            'property_fee_pre_let_expenses',
                                            'property_fee_repairs_provision',
                                            'property_fee_stamp_duty',
                                            'rent_amount',
                                            'rental_fee_allowance_for_voids',
                                            'rental_fee_corporation_tax',
                                            'rental_fee_deferred_fees',
                                            'rental_fee_insurance',
                                            'rental_fee_maintenance_allowance',
                                            'rental_fee_management',
                                            'rental_fee_spv_charge',
                                            'rental_yield',
                                            'share_count',
                                            'share_price',
                                            'slug',
                                            'spv_name',
                                            'target_amount',
                                            'thumbnail_label',
                                            'type',
                                            'documents',
                                            'floorplans',
                                            'legal_documents',
                                            'news_items',
                                            'photos',
                                            'tags')
  end
end
