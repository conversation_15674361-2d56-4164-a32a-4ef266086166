require 'rails_helper'

RSpec.describe PotentialInvestmentItemSerializer do
  let(:potential_investment_item) { create(:potential_investment_item) }
  let(:serializer)  { described_class.new(potential_investment_item) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  subject { JSON.parse(serialization.to_json) }

  it 'includes the expected attributes' do
    expect(subject.keys).to contain_exactly('id',
                                            'property',
                                            'quantity',
                                            'errors')
  end
end
