require 'rails_helper'

RSpec.describe PotentialInvestmentSerializer do
  let(:potential_investment) { create(:potential_investment) }
  let(:serializer)  { described_class.new(potential_investment) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  subject { JSON.parse(serialization.to_json) }

  before(:each) do
    create_list(:potential_investment_item, 5, potential_investment: potential_investment)
  end

  it 'includes the expected attributes' do
    expect(subject.keys).to contain_exactly('id',
                                            'items',
                                            'total',
                                            'user_id',
                                            'errors')
  end
end
