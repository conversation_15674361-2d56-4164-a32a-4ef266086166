require 'rails_helper'

RSpec.describe UserSerializer do
  let(:user) { create(:user_natural) }
  let(:serializer)  { described_class.new(user) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  subject { JSON.parse(serialization.to_json) }

  it 'includes the expected attributes' do
    expect(subject.keys).to contain_exactly('id',
                                            'aasm_state',
                                            'authentication_token',
                                            'business_name',
                                            'business_number',
                                            'call_me',
                                            'confirmed_at',
                                            'country_of_residence',
                                            'created_at',
                                            'date_of_birth',
                                            'email',
                                            'employment_status',
                                            'experience',
                                            'first_name',
                                            'identity_proof',
                                            'income_range',
                                            'last_name',
                                            'legal_type',
                                            'marketing',
                                            'middle_name',
                                            'nationality',
                                            'occupation',
                                            'phone_number',
                                            'planned_investment',
                                            'previous_state',
                                            'reset_token',
                                            'title',
                                            'type',
                                            'updated_at',
                                            'errors',
                                            'directors',
                                            'shareholders',
                                            'address',
                                            'headquarters',
                                            'phone_verified',
                                            'factor_id', 'sca_status')
  end
end
