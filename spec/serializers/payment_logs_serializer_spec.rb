require 'rails_helper'

RSpec.describe PaymentLogSerializer do
  let(:payment_log) { create(:payment_log) }
  let(:serializer)  { described_class.new(payment_log) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  subject { JSON.parse(serialization.to_json) }

  it 'includes the expected attributes' do
    expect(subject.keys).to contain_exactly('id',
                                            'amount',
                                            'created_at',
                                            'direction',
                                            'dividend',
                                            'failed_at',
                                            'kind',
                                            'payout_kind',
                                            'status',
                                            'successful_at',
                                            'user_id',
                                            'errors')
  end
end
