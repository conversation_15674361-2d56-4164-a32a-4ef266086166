require 'rails_helper'

RSpec.describe User::DashboardSerializer do
  let(:serializer) { described_class.new(User::Dashboard.new(create(:user_natural))) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  subject { JSON.parse(serialization.to_json) }

  it 'includes the expected attributes' do
    expect(subject.keys).to contain_exactly('annualised_return',
                                            'profit',
                                            'total_value',
                                            'investments')
  end
end
