require 'rails_helper'

RSpec.describe User::InvestmentSerializer do
  let(:serializer) { described_class.new(create(:investment)) }
  let(:serialization) { ActiveModelSerializers::Adapter.create(serializer) }

  subject { JSON.parse(serialization.to_json) }

  it 'includes the expected attributes' do
    expect(subject.keys).to contain_exactly('dividends',
                                            'property',
                                            'share_logs',
                                            'share_orders',
                                            'total_amount',
                                            'total_growth',
                                            'total_income')
  end
end
