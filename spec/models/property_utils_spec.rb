require 'rails_helper'

RSpec.describe PropertyUtils do
  describe '#rental_yield' do
    context 'with a property with guaranteed_yield' do
      let(:property) { create(:property_regular, guaranteed_yield: 10.99) }

      it { expect(PropertyUtils.rental_yield(property)).to eq(11.0) }
    end

    context 'with a property without guaranteed_yield' do
      let(:property) { create(:property_regular, rent_amount: 10) }

      before { allow(property).to receive(:target_amount).and_return(100) }

      it { expect(PropertyUtils.rental_yield(property)).to eq(10.0) }
    end
  end
end
