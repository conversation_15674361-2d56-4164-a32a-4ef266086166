require 'rails_helper'

RSpec.describe User::TaxStatementItemLoan, type: :model do
  let(:user) { create(:user_natural) }
  let(:investment) {create(:investment, user: user)}
  let(:share_logs) {investment.share_logs}

  let(:tax_statement) do
    User::TaxStatementItemLoan.new
  end

  describe "#sales_proceeds" do
    context "when share logs are present" do
      before do
        share_logs.each {|log| log.update(total_amount: -10)}
        tax_statement.share_logs = share_logs
      end

      it "returns the negative sum of total_amount from share logs" do
        expect(tax_statement.sales_proceeds).to eq(20)
      end
    end

    context "when no share logs are present" do
      before do
        tax_statement.share_logs = []
      end
      it "returns 0" do
        expect(tax_statement.sales_proceeds).to eq(0)
      end
    end
  end

  describe "#total_dividends" do
    it "always returns 0" do
      expect(tax_statement.total_dividends).to eq(0)
    end
  end

  describe "#total_income" do
    context "when property payment logs and rent payment logs are present" do
      before do
        investment.dividends.each {|log| log.update(amount: 100)}
        tax_statement.payment_logs = investment.dividends
      end
      it "returns the sum of amounts from property and rent payment logs" do
        expect(tax_statement.total_income).to eq(200)
      end
    end

    context "when no payment logs are present" do
      before do
        tax_statement.payment_logs = []
      end
      it "returns 0" do
        expect(tax_statement.total_income).to eq(0)
      end
    end
  end
end