require 'rails_helper'

RSpec.describe User::TaxStatement do
  let(:start_date) { Date.today - 1.month }
  let(:end_date) { Date.today + 1.month }
  let(:user) { create(:user_natural) }
  let(:property) { create(:property_regular) }

  let(:tax_statement) do
    User::TaxStatement.new(user: user,
                           start_date: start_date,
                           end_date: end_date)
  end

  let(:property_payout_rent) { create(:property_payout_rent) }

  # Selling Shares
  let(:buy_shares) { create(:share_log, user: user, property: property, quantity: 10) }
  let(:sell_shares) { create(:share_log, user: user, property: property, quantity: -5) }

  # Dividends
  let(:dividend) { create(:property_dividend, user: user, property: property, amount: 5, payout: property_payout_rent) }
  let(:payment_log) { create(:payment_log, kind: 'DIVIDEND', user: user, amount: 4, dividend: dividend) }

  before do
    buy_shares
    payment_log
    sell_shares
  end

  describe '#items' do
    it 'the number of unique properties' do
      expect(tax_statement.items.size).to eq(tax_statement.send(:properties).size)
    end
  end

  describe 'sum functions' do
    %I[investment_costs sales_proceeds total_dividends total_fees subtotal].each do |method|
      it "#{method} is correctly summed" do
        expected_amount = tax_statement.items.sum(&method)

        expect(tax_statement.send(method)).to eq(expected_amount)
      end
    end
  end

  # Private Methods

  describe '#payment_logs_for_period' do
    let!(:past_dividend) { create(:property_dividend, user: user, amount: 5, payout: property_payout_rent) }
    let!(:past_payment_log) { create(:payment_log, kind: 'DIVIDEND', user: user, amount: 4, dividend: past_dividend, created_at: start_date - 1.day) }

    it 'is scoped to time period' do
      expect(tax_statement.send(:payment_logs_for_period).size).to eq(1)
    end
  end

  describe '#sell_logs_for_period' do
    let!(:past_sell_shares) { create(:share_log, user: user, property: property, quantity: -5, created_at: start_date - 1.day) }

    it 'is scoped to time period' do
      expect(tax_statement.send(:sell_logs_for_period).size).to eq(1)
    end
  end
end
