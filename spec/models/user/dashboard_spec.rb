require 'rails_helper'

RSpec.describe User::Dashboard do
  let(:user) { create(:user_natural) }
  let(:dashboard) { create(:dashboard, user: user) }

  # Methods
  describe '#annualised_return' do
    context 'with data' do
      before do
        development = create(:property_development)
        regular = create(:property, property_amount: 100, hpi: 5)
        create(:share_log, user: user, property: regular)
        create(:share_log, user: user, property: development)
      end

      it { expect(dashboard.annualised_return).to eq(0.02) }
      it { expect(dashboard.annualised_return).to be_a(BigDecimal) }
    end

    context 'without data' do
      it { expect(dashboard.annualised_return).to eq(0) }
      it { expect(dashboard.annualised_return).to be_a(BigDecimal) }
    end
  end

  describe '#profit' do
    before do
      allow(dashboard).to receive(:total_value).and_return(10)
      allow(dashboard).to receive(:total_invested).and_return(10)
      allow(dashboard).to receive(:total_dividends).and_return(10)
    end

    it { expect(dashboard.profit).to eq(10 - 10 + 10)}
  end

  describe '#total_value' do
    before do
      create_list(:share_log, 2,
                  quantity: 10,
                  user: user)
    end

    it { expect(dashboard.total_value).to eq(20000) }
  end

  describe '#total_invested' do
    before do
      create_list(:share_log, 2, user: user, total_amount: 10)
    end

    it { expect(dashboard.total_invested).to eq(20) }
  end

  describe '#total_dividends' do
    before do
      create_list(:payment_log, 2, kind: 'DIVIDEND', amount: 100, user: user)
    end

    it { expect(dashboard.total_dividends).to eq(200) }
  end
end
