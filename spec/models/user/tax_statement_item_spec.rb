require 'rails_helper'

RSpec.describe User::TaxStatementItem do
  let(:start_date) { Date.today - 1.month }
  let(:end_date) { Date.today + 1.month }
  let(:user) { create(:user_natural) }
  let(:property) { create(:property_regular) }

  let(:tax_statement) do
    User::TaxStatement.new(user: user,
                           start_date: start_date,
                           end_date: end_date)
  end

  let(:item) { tax_statement.items.first }

  # Selling Shares
  let(:quantity) { 10 }
  let(:buy_shares_1) { create(:share_log, user: user, property: property, quantity: quantity) }
  let(:buy_shares_2) { create(:share_log, user: user, property: property, quantity: quantity) }
  let(:sell_shares_1) { create(:share_log, user: user, property: property, quantity: -quantity) }
  let(:sell_shares_2) { create(:share_log, user: user, property: property, quantity: -quantity) }

  # Payout Rent
  let(:property_payout_rent) { create(:property_payout_rent) }
  let(:dividend_1) { create(:property_dividend, property: property, user: user, amount: 5, payout: property_payout_rent) }
  let(:dividend_2) { create(:property_dividend, property: property, user: user, amount: 5, payout: property_payout_rent) }
  let(:payment_log_1) { create(:payment_log, kind: 'DIVIDEND', user: user, amount: 4, dividend: dividend_1) }
  let(:payment_log_2) { create(:payment_log, kind: 'DIVIDEND', user: user, amount: 4, dividend: dividend_2) }

  # Payout Property
  let(:property_payout_property) { create(:property_payout_property, property: property) }
  let(:payout_dividend) { create(:property_dividend, property: property, user: user, amount: 3, payout: property_payout_property) }
  let(:payout_payment_log) { create(:payment_log, kind: 'DIVIDEND', user: user, amount: 3, dividend: payout_dividend) }

  before do
    buy_shares_1
    buy_shares_2
    payment_log_1
    payment_log_2
    property.update(property_amount: property.property_amount * 2)
    sell_shares_1
    sell_shares_2
    payout_payment_log

    expect(tax_statement.items.size).to eq(1)
  end

  context 'investments' do
    describe '#investment_costs' do
      it 'is based upon the purchase cost' do
        expected_amount = item.send(:average_share_price_paid) * (buy_shares_1.quantity + buy_shares_2.quantity)

        expect(item.investment_costs).to eq(expected_amount)
      end
    end

    context 'when regular property' do
      describe '#sales_proceeds' do
        it 'is the sum of all sell orders and property payouts' do
          expected_amount = -(sell_shares_1.total_amount + sell_shares_2.total_amount) + payout_payment_log.amount

          expect(item.sales_proceeds).to eq(expected_amount)
        end
      end
    end

    context 'when development' do
      let(:property) { create(:property_development) }

      describe '#sales_proceeds' do
        it 'is the sum of all sell orders and property payouts' do
          expected_amount = -(sell_shares_1.total_amount + sell_shares_2.total_amount)

          expect(item.sales_proceeds).to eq(expected_amount)
        end
      end
    end

    describe '#total_fees' do
      it 'is the sum buy and sell fees' do
        expected_amount = item.send(:buy_fees) + item.send(:sell_fees)

        expect(item.total_fees).to eq(expected_amount)
      end
    end

    describe '#sell_quantity' do
      it 'calculated from the sum of all the sell orders quantity' do
        expected_amount = -item.share_logs.sum(&:quantity)

        expect(item.send(:sell_quantity)).to eq(expected_amount)
      end
    end

    describe '#sell_fees' do
      context 'regular property' do
        it 'is 0%' do
          expect(item.send(:sell_fees)).to eq(0)
        end
      end

      context 'easy exit property' do
        let(:property) { create(:property_regular, :easy_exit)}
        let(:payout_payment_log) {} # NOTE: N/A for easy exit properties

        it 'is 3%' do
          expected_amount = item.share_logs.collect { |sl| sl.sell_order.total_fees }.sum

          expect(item.send(:sell_fees)).to eq(expected_amount)
        end
      end
    end

    describe '#buy_fees' do
      it 'the 2% fee when investing' do
        expected_amount = item.investment_costs * Share::BuyOrder::FEE_PERCENTAGE

        expect(item.send(:buy_fees)).to eq(expected_amount)
      end
    end

    describe '#average_share_price_paid' do
      it 'is correctly calculated' do
        expected_amount = (buy_shares_1.total_amount + buy_shares_2.total_amount).to_d /
                          (buy_shares_1.quantity + buy_shares_2.quantity)

        expect(item.send(:average_share_price_paid)).to eq(expected_amount)
      end
    end

  end

  context 'dividends' do
    context 'when regular property' do
      describe '#total_dividends' do
        it 'sums the actual amount paid rather than the amount on the dividend' do
          expected_amount = payment_log_1.amount + payment_log_2.amount

          expect(tax_statement.items.size).to eq(1)
          expect(item.total_dividends).to eq(expected_amount)
        end
      end
    end

    context 'when development' do
      let(:property) { create(:property_development) }

      describe '#total_dividends' do
        it 'sums the actual amount paid rather than the amount on the dividend' do
          expected_amount = payment_log_1.amount + payment_log_2.amount + payout_dividend.amount

          expect(tax_statement.items.size).to eq(1)
          expect(item.total_dividends).to eq(expected_amount)
        end
      end
    end
  end

  describe '#subtotal' do
    it 'is correctly calculated' do
      expect(item.sales_proceeds).to be > 0
      expect(item.investment_costs).to be > 0
      expect(item.total_fees).to be > 0

      expected_amount = item.sales_proceeds - item.investment_costs - item.total_fees

      expect(item.subtotal).to eq(expected_amount)
    end
  end
end
