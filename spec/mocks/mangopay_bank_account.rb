RSpec.configure do |config|
  config.before(:each) do
    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/users/12345/bankaccounts')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_bank_accounts.json").read)

    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/dubit/users/12345/bankaccounts/gb')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_bank_account.json").read)

    stub_request(:post, "https://api.sandbox.mangopay.com/v2.01/dubit/users/12345/bankaccounts/gb")
      .with(body: "{\"OwnerAddress\":{\"AddressLine1\":null,\"AddressLine2\":null,\"City\":null,\"Region\":null,\"PostalCode\":null,\"Country\":null},\"OwnerName\":null,\"SortCode\":null,\"AccountNumber\":null,\"Type\":\"gb\"}")
      .to_return(status: 400, body: File.open("#{Rails.root}/spec/fixtures/mangopay_bank_account_failure.json").read)

    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/users/12345/bankaccounts/0')
      .to_return(status: 400, body: File.open("#{Rails.root}/spec/fixtures/mangopay_bank_account_failure.json").read)

    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/users/12345/bankaccounts/1')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_bank_account.json").read)

    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/users/12345/bankaccounts/2')
      .to_return(status: 400, body: File.open("#{Rails.root}/spec/fixtures/mangopay_bank_account2.json").read)

    stub_request(:put, 'https://api.sandbox.mangopay.com/v2.01/dubit/users/12345/bankaccounts/1')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_bank_account.json").read)
  end
end
