RSpec.configure do |config|
  config.before(:each) do
    stub_request(:post, "https://api.telnyx.com/v2/verify_profiles").
    with(body: "{\"name\":\"Test Profile\",\"sms\":{\"messaging_template_id\":null,\"app_name\":\"Phone\",\"whitelisted_destinations\":[\"GB\"]}}").
    to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/telnyx/profile.json").read, headers: {})
  end

  config.before(:each) do
    stub_request(:get, "https://api.telnyx.com/v2/verify_profiles").
         with(
           headers: {
          'Accept'=>'application/json',
          'Accept-Encoding'=>'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
          'Authorization'=>'Bearer 123456',
          'Content-Type'=>'application/json',
          'User-Agent'=>'Faraday v2.9.2'
           }).
         to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/telnyx/profile.json").read, headers: {})
  end
end
