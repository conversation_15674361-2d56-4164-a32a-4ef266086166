RSpec.configure do |config|
  config.before(:each) do
    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/cards/valid_card_id')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_card.json").read)
  end

  config.before(:each) do
    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/cards/invalid_card_id')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_invalid_card.json").read)
  end

  config.before(:each) do
    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/cards/3d_secure_card_id')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_card_3d_secure.json").read)
  end

  config.before(:each) do
    stub_request(:put, 'https://api.sandbox.mangopay.com/v2.01/dubit/cards/valid_card_id')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_card.json").read)
  end
end
