RSpec.configure do |config|
  config.before(:each) do
    # Cards

    # Success
    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/dubit/payins/card/direct')
      .with(body: /{"AuthorId":"(\d*)","BrowserInfo":\{(.*)\},"CardId":"1","CreditedUserId":"(\d*)","CreditedWalletId":"(\d*)","DebitedFunds":{"Amount":(\d*),"Currency":"GBP"},"Fees":{"Amount":0,"Currency":"GBP"},"IpAddress":(.*),"SecureModeReturnURL":"(.*)"}/)
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_payin.json").read)

    # Failed
    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/dubit/payins/card/direct')
      .with(body: /{"AuthorId":"(\d*)","BrowserInfo":\{(.*)\},"CardId":"2","CreditedUserId":"(\d*)","CreditedWalletId":"(\d*)","DebitedFunds":{Amount":(\d*),"Currency":"GBP"},"Fees":{"Amount":0,"Currency":"GBP"},"IpAddress":(.*),"SecureModeReturnURL":"(.*)"}/)
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_payin_failed.json").read)

    # 3D Secure
    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/dubit/payins/card/direct')
      .with(body: /{"AuthorId":"(\d*)","BrowserInfo":\{(.*)\},"CardId":"3","CreditedUserId":"(\d*)","CreditedWalletId":"(\d*)","DebitedFunds":{"Amount":(\d*),"Currency":"GBP"},"Fees":{"Amount":0,"Currency":"GBP"},"IpAddress":(.*),"SecureModeReturnURL":"(.*)"}/)
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_payin_3d_secure.json").read)

    # Confirm
    stub_request(:get, "https://api.sandbox.mangopay.com/v2.01/dubit/payins/valid_transaction_id")
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_payin.json").read)

    stub_request(:get, "https://api.sandbox.mangopay.com/v2.01/dubit/payins/invalid_transaction_id")
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_payin_failed.json").read)

    # Direct Debits
    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/dubit/mandates/directdebit/web')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_mandate.json").read)

    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/mandates/1')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_mandate.json").read)

    stub_request(:put, 'https://api.sandbox.mangopay.com/v2.01/dubit/mandates/1/cancel')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_mandate.json").read)

    # Bank Wire
    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/dubit/payins/bankwire/direct')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_bank_wire.json").read)

    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/payins/1')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_bank_wire.json").read)
  end
end
