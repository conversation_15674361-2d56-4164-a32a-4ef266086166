RSpec.configure do |config|
  config.before(:each) do
    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/users/12345/wallets')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_wallets.json").read)

    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/users/54321/wallets')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_wallets.json").read)

    stub_request(:get, 'https://api.sandbox.mangopay.com/v2.01/dubit/wallets/1')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_wallet.json").read)

    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/dubit/wallets')
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/mangopay_wallet.json").read)

    stub_request(:get, "https://api.sandbox.mangopay.com/v2.01/dubit/bankaccounts/1/transactions")
      .to_return(status: 200, body: "", headers: {})
  end
end
