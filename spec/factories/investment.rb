FactoryBot.define do
  factory :investment, class: User::Investment do
    skip_create

    transient { user { create(:user_natural) } }

    property { create(:property, property_amount: 100) }
    share_logs { create_list(:share_log, 2, user: user, property: property) }
    share_orders { create_list(:share_buy_order, 2, user: user, property: property) }

    dividends do
      property_payout_rent = create(:property_payout_rent, property: property)
      dividend = create(:property_dividend, user: user, payout: property_payout_rent)

      create_list(:payment_log, 2, user: user, dividend: dividend)
    end

    initialize_with { new(share_logs, share_orders, dividends, property) }
  end
end
