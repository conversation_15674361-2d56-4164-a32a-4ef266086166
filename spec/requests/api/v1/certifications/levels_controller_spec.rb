require 'rails_helper'

RSpec.describe Api::V1::Certifications::LevelsController, type: :request do
  let(:path) { '/api/v1/certifications/levels' }

  describe 'GET #index' do
    it 'returns all certification levels' do
      create(:certification_level)

      get path

      expect(response_json.size).to be > 0
    end

    it 'returns nested questions' do
      create(:certification_level, :with_questions)

      get path

      expect(response_json.first['questions'].size).to eq(5)
      expect(response_json.first['questions'].first['answers'].size).to eq(5)
    end
  end
end
