require 'rails_helper'

RSpec.describe Api::V1::PropertiesController, type: :request do
  let(:path) { '/api/v1/properties' }
  let(:new_property) { create(:property_regular, :with_photos, :with_floorplans, :with_documents, :with_tags) }

  after do
    expect(response).to have_http_status(200)
  end

  describe 'GET #index' do
    it 'returns all properties' do
      create(:property_regular)
      create(:property_development)

      get path

      expect(response_json.size).to eq(2)
    end

    it 'doesnt return hidden properties' do
      visible_property = create(:property_regular, visible: true)
      hidden_property = create(:property_regular, visible: false)

      get path

      expect(response_json.size).to eq(1)
      expect(response.body).to include(visible_property.name)
      expect(response.body).not_to include(hidden_property.name)
    end

    it 'doesnt return causes' do
      property = create(:property_regular, visible: true)
      cause = create(:property_cause, visible: false)

      get path

      expect(response_json.size).to eq(1)
      expect(response.body).to include(property.name)
      expect(response.body).not_to include(cause.name)
    end

    it 'returns nested photos' do
      create(:property_regular, :with_photos)

      get path

      expect(response_json.first['photos'].size).to eq(5)
    end

    it 'returns nested floorplans' do
      create(:property_regular, :with_floorplans)

      get path

      expect(response_json.first['floorplans'].size).to eq(5)
    end

    it 'returns nested documents' do
      create(:property_regular, :with_documents)

      get path

      expect(response_json.first['documents'].size).to eq(5)
    end

    it 'returns nested tags' do
      create(:property_regular, :with_tags)

      get path

      expect(response_json.first['tags'].size).to eq(5)
    end
  end

  describe 'GET #show' do
    it 'returns a single property' do
      get singular_path(new_property.id)

      expect(response_json['documents'].size).to eq(5)
      expect(response_json['floorplans'].size).to eq(5)
      expect(response_json['photos'].size).to eq(5)
    end
  end

  describe 'GET #by_slug' do
    it 'returns a single property' do
      get '/api/v1/properties/by_slug', params: { slug: new_property.slug }

      expect(response).to have_http_status(200)
    end
  end

  def singular_path(id)
    "/api/v1/properties/#{id}"
  end
end
