require 'rails_helper'

RSpec.describe Api::V1::InvestmentDocumentsController, type: :request do
  let(:path) { '/api/v1/investment_documents' }
  let(:quantity) { 10 }

  before(:each) do
    create_list(:investment_document, quantity)
  end

  describe 'GET #index' do
    it 'returns all investment documents' do
      get path

      expect(response).to have_http_status(200)
      expect(response_json.size).to eq(quantity)
    end
  end
end
