require 'rails_helper'

RSpec.describe Api::V1::Payins::BankWiresController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:auth_headers) { auth_headers_for_user(user) }
  let(:request) { post singular_path(user), params: { amount: 2000 }, headers: auth_headers }

  describe 'POST #create' do
    it 'successfully create a payin' do
      request

      expect(response).to have_http_status(200)

      [:id, :amount, :bic, :fees, :iban, :owner_name, :reference, :type].each do |attribute|
        expect(response_json).to have_key(attribute.to_s)
      end
    end

    it 'successfully logs payin' do
      PaymentLog.destroy_all

      request

      expect(response).to have_http_status(200)

      payment_log = PaymentLog.first
      expect(payment_log.direction).to eq('credit')
      expect(payment_log.kind).to eq('BANK_WIRE')
      expect(payment_log.status).to eq('CREATED')
    end

    it 'fails without an amount passed' do
      post singular_path(user), headers: auth_headers

      expect(response).to have_http_status(422)
      expect(response.body).to include('Amount cannot be blank')
    end

    it 'sends the email' do
      expect { request }.to have_enqueued_job.on_queue('default').twice
    end
  end

  def singular_path(user)
    "/api/v1/users/#{user.id}/payins/bank_wires"
  end
end
