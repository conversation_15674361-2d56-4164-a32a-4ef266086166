require 'rails_helper'

RSpec.describe Api::V1::Payins::GooglePaysController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:auth_headers) { auth_headers_for_user(user) }
  let(:token) { 'google_pay_token_123' }
  let(:amount) { 1000 }
  let(:fee) { 10}
  let(:potential_investment_id) { nil }
  let(:browser_infos) do
    {
      JavaEnabled: true,
      Language: 'en-US',
      ColorDepth: 24,
      ScreenHeight: 1080,
      ScreenWidth: 1920,
      TimeZoneOffset: -330,
      JavascriptEnabled: true,
      AcceptHeader: 'application/json',
      IpAddress: '127.0.0.1',
      UserAgent: 'Mozilla/5.0'
    }
  end
  let(:create_request) { post api_v1_user_payins_google_pays_url(user_id: user.id), params: { amount: amount, token: token, fee: fee, potential_investment_id: potential_investment_id, browser_infos: browser_infos }, headers: auth_headers }
  let(:handle_response) { get handle_response_api_v1_user_payins_google_pays_url(user_id: user.id), params: { transactionId: transaction_id, p_inv_id: potential_investment_id }, headers: auth_headers }


  describe 'POST #create' do
    context 'when the Google Pay transaction is successful' do
      let(:transaction) do
        {
          'Id' => 'transaction_id_123',
          'Status' => 'SUCCEEDED',
          'CreditedFunds' => { 'Amount' => amount },
          'ResultMessage' => 'Transaction succeeded',
          'PaymentType' => 'GOOGLE_PAY'
        }
      end

      before do
        allow(Mango::GooglePayService).to receive(:new).and_return(double(call: double(success?: true, data: double(body: transaction.to_json))))
        allow(MangoPay::PayIn).to receive(:fetch).and_return(transaction)
        create_request
      end

      it 'creates a payment log' do
        expect(PaymentLog.count).to eq(1)
        payment_log = PaymentLog.first
        expect(payment_log.direction).to eq('credit')
        expect(payment_log.kind).to eq('GOOGLE_PAY')
        expect(payment_log.status).to eq('SUCCEEDED')
      end

      it 'sends a success email if potential_investment_id is blank' do
        expect(queue_job?('PayinsCardMailer','successful')).to be true
      end

      it 'returns a successful response' do
        expect(response).to have_http_status(:success)
        expect(json_response).to eq(transaction)
      end
    end

    context 'when the Google Pay transaction fails' do
      let(:error_response) do
        {
          'Errors' => {
            'Amount' => 'Invalid amount',
            'Token' => 'Invalid token'
          }
        }
      end

      before do
        allow(Mango::GooglePayService).to receive(:new).and_return(double(call: double(success?: false, error: error_response.to_json)))
        allow(Airbrake).to receive(:notify)
      end

      it 'notifies Airbrake with the error message' do
        expect(Airbrake).to receive(:notify).with("Amount : Invalid amount and Token : Invalid token")
        create_request
      end

      it 'returns an error response' do
        create_request
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['errors']['base']).to eq(["Amount : Invalid amount and Token : Invalid token"])
      end
    end
  end

    describe 'POST #handle_response' do
      let(:transaction_id) { 'transaction_id_123' }
      let(:transaction) do
        {
          'Id' => transaction_id,
          'Status' => 'SUCCEEDED',
          'ResultMessage' => 'Transaction succeeded'
        }
      end

      before do
        allow(MangoPay::PayIn).to receive(:fetch).and_return(transaction)
      end

      context 'when the transaction is successful' do
        it 'updates the payment log status' do
          log = create(:payment_log, user: user, potential_investment_id: potential_investment_id, kind: 'GOOGLE_PAY')
          expect(log.status).to eq 'CREATED'
          handle_response
          expect(log.reload.status).to eq 'SUCCEEDED'
        end

        it 'sends a success email if potential_investment_id is blank' do
          handle_response
          expect(queue_job?('PayinsCardMailer','successful')).to be true
        end

        it 'returns a successful response' do
          handle_response
          expect(response).to have_http_status(:success)
          expect(json_response).to eq(transaction)
        end
      end

      context 'when the transaction fails' do
        let(:transaction) do
          {
            'Id' => transaction_id,
            'Status' => 'FAILED',
            'ResultMessage' => 'Transaction failed'
          }
        end

        it 'raises an error' do
          handle_response
          expect(response).to have_http_status(:unprocessable_entity)
          expect(json_response['errors']['base']).to eq(["Transaction FAILED: Transaction failed"])
        end
      end
    end

  private

  def json_response
    JSON.parse(response.body)
  end
end
