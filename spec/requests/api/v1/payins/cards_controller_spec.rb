require 'rails_helper'

RSpec.describe Api::V1::Payins::CardsController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:potential_investment) { create(:potential_investment, user: user)}
  let(:payin_json) { JSON.parse(File.open("#{Rails.root}/spec/fixtures/mangopay_card_registration.json").read) }
  let(:auth_headers) { auth_headers_for_user(user) }

  before do
    ActionMailer::Base.deliveries.clear
  end

  describe 'POST #create' do
    it 'correctly returns required params' do
      post singular_path(user), headers: auth_headers

      expect(response).to have_http_status(201)
      expect(response_json['access_key']).to eq(payin_json['AccessKey'])
      expect(response_json['card_registration_url']).to eq(payin_json['CardRegistrationURL'])
      expect(response_json['id']).to eq(payin_json['Id'])
      expect(response_json['pre_registration_data']).to eq(payin_json['PreregistrationData'])
    end
  end

  describe 'PUT #update' do
    it 'successfully takes payment without browser info' do
      put plural_path(user, 'valid_card_id'), params: { amount: 2000 }, headers: auth_headers

      expect(response).to have_http_status(200)
    end

    it 'successfully takes payment with browser info' do
      browser_info = {
        accept_header: 'example accept header',
        color_depth: 32,
        ip_address: '********',
        java_enabled: 'true',
        javascript_enabled: 'true',
        language: 'en',
        screen_height: 1024,
        screen_width: 768,
        time_zone_offset: 0,
        user_agent: 'example user agent'
      }

      put plural_path(user, 'valid_card_id'), params: { amount: 2000, browser_info: browser_info }, headers: auth_headers

      expect(response).to have_http_status(200)
    end

    it 'should enqueue a mailer job' do
      expect do
        put plural_path(user, 'valid_card_id'), params: { amount: 2000 }, headers: auth_headers
      end.to have_enqueued_job.on_queue('default').exactly(2).times
      # 1st for admin notification
      # 2nd for send_receipt
    end

    it 'successfully logs payment' do
      total_credited_before = user.amount_credited
      PaymentLog.destroy_all

      put plural_path(user, 'valid_card_id'), params: { amount: 2000 }, headers: auth_headers

      expect(response).to have_http_status(200)
      expect(user.reload.amount_credited).to eq(total_credited_before + 2000)

      payment_log = PaymentLog.first
      expect(payment_log.direction).to eq('credit')
      expect(payment_log.kind).to eq('CARD')
      expect(payment_log.status).to eq('SUCCEEDED')
      expect(payment_log.user_id).to eq(user.id)
      expect(payment_log.successful_at).not_to eq(nil)

      expect(user.amount_credited).to eq(2000)
    end

    context 'with potential investment' do
      it 'doesnt send receipt when potential investment is passed' do
        put plural_path(user, 'valid_card_id'), params: { amount: 2000, potential_investment_id: potential_investment }, headers: auth_headers

        expect(response).to have_http_status(200)
        expect(ActionMailer::Base.deliveries.count).to eq(0)
      end
    end

    it 'doesnt take payment from someone elses card' do
      put plural_path(user, 'invalid_card_id'), params: { amount: 2000 }, headers: auth_headers

      expect(response).to have_http_status(422)
      expect(response.body).to include('Wallet does not belong to that user')
    end

    it 'errors when no amount is passed' do
      put plural_path(user, 'valid_card_id'), headers: auth_headers

      expect(response).to have_http_status(422)
      expect(response.body).to include('Amount cannot be blank')
    end
  end

  def singular_path(user)
    "/api/v1/users/#{user.id}/payins/cards"
  end

  def plural_path(user, card_id)
    "/api/v1/users/#{user.id}/payins/cards/#{card_id}"
  end
end
