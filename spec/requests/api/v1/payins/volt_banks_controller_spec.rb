require 'rails_helper'

RSpec.describe Api::V1::Payins::VoltBanksController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:auth_headers) { auth_headers_for_user(user) }
  let(:payment_log) { create(:payment_log, user: user) }
  let(:payment) { payment_data(payment_log.id, nil) }
  let(:error) { { details: ['description' => 'Transaction failed'] } }

  describe 'GET #index' do
    context 'when Nuapay list all open banks' do
      let(:open_banks_list) { [{ 'id'=> '8ow24y2pdx', 'uri'=>'/banks/8ow24y2pdx', 'name'=>'nua_pay_test'}] }
      before do
        allow(Nuapay::Banks::List).to receive(:new).and_return(double(call: double(body: { data: open_banks_list }.to_json)))
        get api_v1_user_payins_open_banks_url(user_id: user.id), params: {}, headers: auth_headers
      end

      it 'list all open banks' do
        expect(json_response).to eq(open_banks_list)
      end
    end
  end

  describe 'POST #create' do
    context 'when the transaction is successful' do
      it 'create initialize the payment' do
        allow(Nuapay::Payments::Create).to receive(:new).and_return(double(call: double(success?: true, body: payment.to_json)))

        post api_v1_user_payins_open_banks_url(user_id: user.id), params: {}, headers: auth_headers
        expect(json_response).to eq(payment)
      end
    end

    context 'when the transaction fails' do
      it 'raises an error' do
        allow(Nuapay::Payments::Create).to receive(:new).and_return(double(call: double(success?: false, body: error.to_json)))

        post api_v1_user_payins_open_banks_url(user_id: user.id), params: {}, headers: auth_headers
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['base']).to eq(["Transaction failed"])
      end
    end
  end

  describe 'GET #show' do
    context 'when the transaction is successful' do
      it 'retrieve the payment' do
        allow(Nuapay::Payments::Retrieve).to receive(:new).and_return(double(call: double(success?: true, body: payment.to_json)))

        get api_v1_user_payins_open_bank_url('re27gry2dw', user_id: user.id), params: {}, headers: auth_headers
        expect(json_response).to eq(payment)
      end
    end

    context 'when the transaction fails' do
      it 'raises an error' do
        allow(Nuapay::Payments::Retrieve).to receive(:new).and_return(double(call: double(success?: false, body: error.to_json)))

        get api_v1_user_payins_open_bank_url('re27gry2dw', user_id: user.id), params: {}, headers: auth_headers
        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['base']).to eq(["Transaction failed"])
      end
    end
  end

  describe 'GET #handle_response' do
    context 'when the status is succeeded' do
      it 'generate the webhook' do
        allow(Nuapay::Webhooks::Create).to receive(:new).and_return(double(call: double(success?: true)))

        expect( get handle_response_api_v1_user_payins_open_banks_url(user_id: user.id), params: {log_id: payment_log.id, status: 'success'}, headers: auth_headers
        ).to eq(204)
      end
    end

    context 'when the status fails' do
      it 'raises an error' do
        get handle_response_api_v1_user_payins_open_banks_url(user_id: user.id), params: {log_id: payment_log.id, status: 'failed'}, headers: auth_headers
        expect(payment_log.reload.status).to eq('FAILED')
      end
    end
  end

  describe 'GET #payment_complete' do
    context 'when the status is succeeded' do
      it 'will not update the log status ' do
        allow(Nuapay::Payments::Retrieve).to receive(:new).and_return(double(call: double(success?: true, body: payment.to_json)))
        get payment_complete_api_v1_user_payins_open_banks_url(user_id: user.id), params: {}, headers: auth_headers
        expect(payment_log.reload.status).to eq ('CREATED')
      end
    end

    context 'when the status fails' do
      let(:payment) { payment_data(payment_log.id, 'SETTLEMENT_REJECTED') }
      it 'update the log status to failed' do
        allow(Nuapay::Payments::Retrieve).to receive(:new).and_return(double(call: double(success?: true, body: payment.to_json)))
        get payment_complete_api_v1_user_payins_open_banks_url(user_id: user.id), params: {}, headers: auth_headers
        expect(payment_log.reload.status).to eq ('FAILED')
      end
    end
  end

  private

  def json_response
    JSON.parse(response.body)
  end
  def payment_data(log_id, payment_status='PENDING_APPROVAL')
    {
      "uri"=> "/payments/re27gry2dw",
      "data"=> {
        "id"=> "re27gry2dw",
        "uri"=> "/payments/re27gry2dw",
        "endToEndIdentification"=> "endToEndIdentification123",
        "debtorAccount"=> {},
        "merchantPostAuthUrl"=> "https://merchant.openbanking.com/result?log_id=#{log_id}",
        "status"=> payment_status,
      },
      "amount"=> 99.99,
      "currency"=> "GBP",
      "countryCode"=> "GB",
      "language"=> "en",
      "email"=> "<EMAIL>",
      "integrationType"=> "SELF_HOSTED",
      "bankId"=> "lyboxy9bqp",
      "aspspAuthUrl"=> "https://example.com",

    }
  end
end
