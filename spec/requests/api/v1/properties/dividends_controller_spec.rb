require 'rails_helper'

RSpec.describe Api::V1::Properties::DividendsController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:user2) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:property) { create(:property_regular) }
  let(:property_payout_rent) { create(:property_payout_rent, property: property) }
  let(:quantity) { 5 }
  let(:start_date) { 3.weeks.ago }
  let(:end_date) { 1.week.ago }

  let(:auth_headers) { auth_headers_for_user(user) }

  describe 'GET #index' do
    before(:each) do
      quantity.times do
        create(:property_dividend, user: user, property: property, payout: property_payout_rent)
      end
    end

    it 'correctly returns a list of dividends' do
      get singular_path(user), headers: auth_headers

      expect(response).to have_http_status(200)
      expect(response_json.size).to eq(quantity)
    end

    it 'doesnt return someone else dividends' do
      quantity.times { create(:share_buy_order, user: user2) }

      get singular_path(user), headers: auth_headers

      expect(response).to have_http_status(200)
      expect(response_json.size).to eq(quantity)

      user_ids = response_json.collect { |sl| sl['user_id'] }
      expect(user_ids).not_to include(user2.id)
    end

    it 'should accept a date range' do
      create(:property_dividend, user: user, property: property, created_at: 2.weeks.ago, payout: property_payout_rent)

      get singular_path(user), params: { start_date: start_date, end_date: end_date }, headers: auth_headers

      expect(response).to have_http_status(200)
      expect(response_json.size).to eq(1)
      expect(user.dividends.count).to be > 1
    end

    it 'should error if dates are the wrong order' do
      get singular_path(user), params: { start_date: end_date, end_date: start_date }, headers: auth_headers

      expect(response).to have_http_status(422)
    end
  end

  def singular_path(user)
    "/api/v1/users/#{user.id}/properties/dividends"
  end
end
