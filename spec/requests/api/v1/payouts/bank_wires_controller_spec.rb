require 'rails_helper'

RSpec.describe Api::V1::Payouts::BankWiresController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:auth_headers) { auth_headers_for_user(user) }
  let(:bank_account) { user.bank_accounts.first }

  describe 'POST #create' do
    context 'when not kyc regular' do
      let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }

      it 'returns a 404' do
        post singular_path(user), params: { amount: 2000, bank_account_id: bank_account.id }, headers: auth_headers

        expect(response).to have_http_status(404)
      end
    end

    context 'when kyc regular or pending' do
      it 'successfully takes payment when regular' do
        post singular_path(user), params: { amount: 2000, bank_account_id: bank_account.id }, headers: auth_headers

        expect(response).to have_http_status(200)
      end

      context 'pending user' do
        let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }

        it 'successfully takes payment when pending' do
          post singular_path(user), params: { amount: 2000, bank_account_id: user.bank_accounts.first.id }, headers: auth_headers

          expect(response).to have_http_status(200)
        end
      end
    end

    it 'successfully logs payment' do
      PaymentLog.destroy_all

      post singular_path(user), params: { amount: 2000, bank_account_id: bank_account.id }, headers: auth_headers

      expect(response).to have_http_status(200)

      payment_log = PaymentLog.first
      expect(payment_log.direction).to eq('debit')
      expect(payment_log.kind).to eq('BANK_WIRE')
      expect(payment_log.status).to eq('CREATED')
    end

    it 'doesnt take payment from someone elses card' do
      post singular_path(user), params: { amount: 2000, bank_account_id: 2 }, headers: auth_headers

      expect(response).to have_http_status(422)
      expect(response.body).to include('Bank account id is invalid or does not belong to that user')
    end

    it 'errors when no amount is passed' do
      post singular_path(user), params: { bank_account_id: bank_account.id }, headers: auth_headers

      expect(response).to have_http_status(422)
      expect(response.body).to include('Amount cannot be blank')
    end

    it 'errors when no params passed' do
      post singular_path(user), headers: auth_headers

      expect(response).to have_http_status(422)
      expect(response.body).to include('Amount cannot be blank')
    end

    context 'kyc pending' do
      let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }
      let(:request) { post singular_path(user), params: { amount: 2000, bank_account_id: bank_account.id }, headers: auth_headers }

      it 'queues for later' do
        expect { request }.to change { User::QueuedAction.count }.by(1)
      end
    end
  end

  def singular_path(user)
    "/api/v1/users/#{user.id}/payouts/bank_wires"
  end
end
