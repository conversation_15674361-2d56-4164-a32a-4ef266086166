require 'rails_helper'

RSpec.describe Api::V1::Shares::LogsController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:kyc_regular_submitted_user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }
  let(:quantity) { 5 }
  let(:property) { create(:property_regular) }
  let(:order) { create(:share_buy_order, user: user, property: property) }
  let(:start_date) { 3.weeks.ago }
  let(:end_date) { 1.week.ago }

  let(:auth_headers) { auth_headers_for_user(user) }

  before(:each) do
    quantity.times { create(:share_log, user: user, buy_order: order, property: property) }
  end

  describe 'GET #index' do
    it 'correctly returns a list of logs' do
      get index_path(user), headers: auth_headers

      expect(response).to have_http_status(200)
      expect(response_json.size).to eq(quantity)
    end

    it 'doesnt return someone else logs' do
      second_order = create(:share_buy_order, user: kyc_regular_submitted_user, property: property)
      quantity.times { create(:share_log, user: kyc_regular_submitted_user, buy_order: second_order, property: property) }

      get index_path(user), headers: auth_headers

      expect(response).to have_http_status(200)
      expect(response_json.size).to eq(quantity)

      user_ids = response_json.collect { |sl| sl['user_id'] }
      expect(user_ids).not_to include(kyc_regular_submitted_user.id)
    end

    it 'should accept a date range' do
      create(:share_log, user: user, buy_order: order, property: property, created_at: 2.weeks.ago)

      get index_path(user), params: { start_date: start_date, end_date: end_date }, headers: auth_headers

      expect(response).to have_http_status(200)
      expect(response_json.size).to eq(1)
      expect(user.share_logs.count).to be > 1
    end

    it 'should error if dates are the wrong order' do
      get index_path(user), params: { start_date: end_date, end_date: start_date }, headers: auth_headers

      expect(response).to have_http_status(422)
    end
  end

  def index_path(user)
    "/api/v1/users/#{user.id}/shares/logs"
  end
end
