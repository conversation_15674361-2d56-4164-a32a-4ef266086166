require 'rails_helper'

RSpec.describe Api::V1::Shares::OrdersController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:kyc_regular_submitted_user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }
  let(:property) { create(:property_regular) }
  let(:quantity) { 5 }

  let(:auth_headers) { auth_headers_for_user(user) }

  describe 'GET #index' do
    before(:each) do
      quantity.times do
        create(:share_buy_order, user: user, property: property)
      end
    end

    it 'correctly returns a list of orders' do
      get singular_path(user), headers: auth_headers

      expect(response).to have_http_status(200)

      expect(response_json.size).to eq(quantity)
    end

    it 'doesnt return someone else orders' do
      quantity.times { create(:share_buy_order, user: kyc_regular_submitted_user) }

      get singular_path(user), headers: auth_headers

      expect(response).to have_http_status(200)
      expect(response_json.size).to eq(quantity)

      user_ids = response_json.collect { |sl| sl['user_id'] }
      expect(user_ids).not_to include(kyc_regular_submitted_user.id)
    end
  end

  describe 'GET #new' do
    it 'should return an object with quantity_available_to_sell' do
      get new_path(user), headers: auth_headers

      expect(response).to have_http_status(200)
      expect(response_json['quantity_available_to_sell']).to eq(0)
    end
  end

  describe 'POST #create' do
    describe 'buy orders' do
      it 'successfully creates the order' do
        orders_before = Share::BuyOrder.count

        post singular_path(user), params: { property_id: property.id,
                                                           quantity: quantity,
                                                           kind: 'buy' },
                                                 headers: auth_headers

        expect(response).to have_http_status(201)
        expect(Share::BuyOrder.count).to eq(orders_before + 1)
      end

      it 'fails when no params passed' do
        post singular_path(user), headers: auth_headers

        expect(response).to have_http_status(422)

        expect(response_json['errors']['property'].size).to be > 0
        expect(response_json['errors']['quantity'].size).to be > 0
      end
    end

    describe 'sell / easy_exit orders' do
      let(:invalid_property) { create(:property_regular, :unfunded) }
      let(:buy_shares) { create(:share_log, user: user, property: property, quantity: quantity) }
      let(:destroy_shares) { Share::Log.where(user: user, property: property).destroy_all }

      context 'sell' do
        it_behaves_like 'it acts like a sell order' do
          let(:kind) { 'sell' }
        end
      end

      context 'easy_exit' do
        it_behaves_like 'it acts like a sell order' do
          let(:kind) { 'easy_exit' }
        end
      end
    end
  end

  describe 'GET #show' do
    let(:share_order) { create(:share_buy_order, user: user, property: property) }
    let(:another_user) { create(:user_natural, :confirmed, :kyc_light_complete) }

    it 'returns a single share log' do
      expect(:share_order).not_to eq(nil)

      get show_path(user, share_order.id), headers: auth_headers

      expect(response).to have_http_status(200)
    end

    it 'returns a 404 if trying to access someone elses record' do
      get show_path(another_user, share_order.id), headers: auth_headers_for_user(another_user)

      expect(response).to have_http_status(404)
    end

    it 'returns a 404 if the record doesnt exist' do
      get show_path(another_user, -1), headers: auth_headers_for_user(another_user)

      expect(response).to have_http_status(404)
    end
  end

  describe 'DELETE cancel' do
    let(:quantity) { 10 }
    let(:buy_shares) { create(:share_log, property: property, user: user, quantity: quantity) }
    let(:share_sell_order) { create(:share_sell_order, property: property, user: user, quantity: quantity) }

    before(:each) do
      buy_shares
    end

    it 'queues the order for cancellation' do
      delete cancel_path(user, share_sell_order), headers: auth_headers

      expect(response).to have_http_status(200)
    end

    it 'won\'t cancel an already cancelled sell order' do
      share_sell_order.update(aasm_state: 'cancelled')

      delete cancel_path(user, share_sell_order), headers: auth_headers

      expect(response).to have_http_status(422)
      expect(response.body).to include('Order has already been cancelled')
    end
  end

  def new_path(user)
    "/api/v1/users/#{user.id}/shares/orders/new?kind=sell"
  end

  def show_path(user, id)
    "/api/v1/users/#{user.id}/shares/orders/#{id}"
  end

  def cancel_path(user, share_sell_order)
    "/api/v1/users/#{user.id}/shares/orders/#{share_sell_order.id}/cancel?kind=sell"
  end

  def singular_path(user)
    "/api/v1/users/#{user.id}/shares/orders"
  end

  def multiple_path(user)
    "/api/v1/users/#{user.id}/shares/orders/create_multiple"
  end
end
