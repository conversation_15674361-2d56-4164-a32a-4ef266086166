require 'rails_helper'

RSpec.describe Api::V1::Users::KycsController, type: :request do
  let(:kyc_light_complete_params) { attributes_for(:user_natural, :kyc_light_complete) }
  let(:kyc_regular_submitted_params) { attributes_for(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }
  let(:user) { create(:user_natural, :confirmed) }
  let(:kyc_light_user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:document) { create(:user_mangopay_kyc_document)}

  before do
    allow_any_instance_of(::FlgCrm::Users).to receive(:sync_to_crm).and_return(OpenStruct.new(success?: true, errors: ''))
  end
  describe 'POST #create' do
    context 'KYC natural success' do
      it 'accepts a valid params for light level' do
        post singular_path(user.id), params: kyc_light_complete_params.merge(level: 'light',
                                                                             address_attributes: attributes_for(:user_address, kind: 'personal'),
                                                                             authentication_token: user.authentication_token),
                                     headers: auth_headers_for_user(user)

        expect(response).to have_http_status(201)

        %w(middle_name address_1 city country post_code phone_number call_me nationality).each do |attribute|
          expect(response_json[attribute]).to eq(kyc_light_complete_params[attribute.to_sym])
        end

        expect(Date.parse(response_json['date_of_birth'])).to eq(kyc_light_complete_params[:date_of_birth].to_date)
        expect(response_json['aasm_state']).to eq('kyc_light_is_complete')
      end

      it 'accepts a valid params for regular level' do
        post singular_path(kyc_light_user.id), params: kyc_regular_submitted_params.merge(level: 'regular',
                                                                                               authentication_token: kyc_light_user.authentication_token),
                                                    headers: auth_headers_for_user(kyc_light_user)

        expect(response).to have_http_status(201)

        %w(income_range identity_proof occupation).each do |attribute|
          expect(response_json[attribute]).to eq(kyc_regular_submitted_params[attribute.to_sym])
        end

        expect(response_json['aasm_state']).to eq('kyc_regular_is_pending')
        expect(kyc_light_user.reload.mangopay_id).not_to eq(nil)
      end

      it 'accepts optional params' do
        params = kyc_light_complete_params.merge(title: 'Mrs',
                                                 first_name: 'Daisy',
                                                 last_name: 'Duke',
                                                 country_of_residence: 'ES')

        post singular_path(user.id), params: params.merge(level: 'light',
                                                          address_attributes: attributes_for(:user_address, kind: 'personal'),
                                                          authentication_token: user.authentication_token),
                                     headers: auth_headers_for_user(user)

        expect(response).to have_http_status(201)

        %w(title first_name last_name country_of_residence).each do |attribute|
          expect(response_json[attribute]).to eq(params[attribute.to_sym])
        end
      end
    end
    context 'KYC natural fail' do
      it 'throw unprocessable entity when kind attribute is blank' do
        post singular_path(user.id), params: kyc_light_complete_params.merge(level: 'light',
                                                                             address_attributes: attributes_for(:user_address, kind: ''),
                                                                             authentication_token: user.authentication_token),
             headers: auth_headers_for_user(user)

        expect(response).to have_http_status(422)
      end
    end

    context 'legal' do
      let(:kyc_light_complete_params) { attributes_for(:user_legal, :kyc_light_complete) }
      let(:kyc_regular_submitted_params) { attributes_for(:user_legal, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }
      let(:user) { create(:user_legal, :confirmed) }

      it 'accepts a valid params for light level' do
        post singular_path(user.id), params: kyc_light_complete_params.merge(level: 'light',
                                                                             address_attributes: attributes_for(:user_address, kind: 'personal'),
                                                                             authentication_token: user.authentication_token),
                                     headers: auth_headers_for_user(user)

        expect(response).to have_http_status(201)
        expect(response_json['aasm_state']).to eq('kyc_light_is_complete')
      end

      it 'accepts a valid params for regular level' do
        post singular_path(kyc_light_user.id), params: kyc_regular_submitted_params.merge(level: 'regular', income_range: '100', occupation: 'Engineer', identity_proof: 'pdf',
                                                                                               # headquarters_attributes: attributes_for(:user_address, kind: 'headquarters'),
                                                                                               authentication_token: kyc_light_user.authentication_token),
                                                    headers: auth_headers_for_user(kyc_light_user)

        expect(response).to have_http_status(201)
        expect(response_json['aasm_state']).to eq('kyc_regular_is_pending')
        expect(kyc_light_user.reload.mangopay_id).not_to eq(nil)
      end
    end
    context 'KYC legal fail' do
      it 'throw unprocessable entity when kind attribute is blank' do
        post singular_path(user.id), params: kyc_light_complete_params.merge(level: 'legal',
                                                                             address_attributes: attributes_for(:user_address, kind: ''),
                                                                             authentication_token: user.authentication_token),
             headers: auth_headers_for_user(user)

        expect(response).to have_http_status(422)
      end
    end

    it 'errors correctly with no level' do
      post singular_path(user.id), params: kyc_light_complete_params.merge(authentication_token: user.authentication_token),
                                   headers: auth_headers_for_user(user)

      expect(response).to have_http_status(422)
      expect(response_json['errors']['base']).to eq(['Level must be specified'])
    end

    it 'errors correctly with bogus level' do
      post singular_path(user.id), params: kyc_light_complete_params.merge(level: 'bogus',
                                                                           authentication_token: user.authentication_token),
                                   headers: auth_headers_for_user(user)

      expect(response).to have_http_status(422)
      expect(response_json['errors']['base']).to eq(['Level is invalid'])
    end

    def singular_path(id)
      "/api/v1/users/#{id}/kycs"
    end
  end

  describe 'POST #check' do
    let(:kyc_regular_user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
    let(:max_credit) { User::MAX_KYC_CREDIT }

    it 'returns 200 when under credit' do
      expect(kyc_light_user.amount_credited).to eq(0)

      post check_path(kyc_light_user), params: { credit: (max_credit - 1) }, headers: auth_headers_for_user(kyc_light_user)

      expect(response).to have_http_status(200)
    end

    it 'returns 422 when over credit' do
      post check_path(kyc_light_user), params: { credit: (max_credit + 1) }, headers: auth_headers_for_user(kyc_light_user)
      expect(response).to have_http_status(422)
    end

    it 'always returns 200 for kyc regular users' do
      post check_path(kyc_regular_user), params: { credit: (max_credit + 500) }, headers: auth_headers_for_user(kyc_regular_user)
      expect(response).to have_http_status(200)
    end

    def check_path(user)
      "/api/v1/users/#{user.id}/kycs/check"
    end
  end

  describe '#webhook' do
    before do
      allow(MangoPay::KycDocument).to receive(:fetch).and_return({ 'UserId' => user.mangopay_id })
      allow(User).to receive(:find_by).and_return(user)
      allow(user).to receive(:update)
      allow(User::KycDocument).to receive(:find_by).and_return(document)
      allow(document).to receive(:update)
      allow(KYCMailer).to receive_message_chain(:document_outdated, :deliver_later)
      allow(Airbrake).to receive(:notify)
    end

    context 'when EventType is KYC_OUTDATED' do
      let(:params) { { event_type: 'KYC_OUTDATED', resource_id: '67890' } }

      it 'fetches the KYC document and updates the user state' do
        get webhook_api_v1_user_kycs_url(user_id: user.id), params: params

        expect(MangoPay::KycDocument).to have_received(:fetch).with(nil, params[:resource_id])
        expect(User).to have_received(:find_by).with(mangopay_id: user.mangopay_id)
        expect(user).to have_received(:update).with(aasm_state: 'kyc_regular_is_required')
        expect(User::KycDocument).to have_received(:find_by).with(kyc_document_id: params[:resource_id])
        expect(document).to have_received(:update).with(state: 'OUT_OF_DATE')
        expect(KYCMailer).to have_received(:document_outdated).with(user)
        expect(Airbrake).to have_received(:notify).with(ActionController::Parameters.new(params.merge!("controller"=>"api/v1/users/kycs", "action"=>"webhook", "user_id"=>"#{user.id}")))
      end
    end

    context 'when EventType is USER_KYC_LIGHT' do
      let(:params) { { event_type: 'USER_KYC_LIGHT', resource_id: user.mangopay_id } }

      it 'finds the user and updates the user state' do
        get webhook_api_v1_user_kycs_url(user_id: user.id), params: params

        expect(User).to have_received(:find_by).with(mangopay_id: params[:resource_id])
        expect(user).to have_received(:update).with(aasm_state: 'kyc_regular_is_required')
        expect(KYCMailer).to have_received(:document_outdated).with(user)
        expect(Airbrake).to have_received(:notify).with(ActionController::Parameters.new(params.merge!("controller"=>"api/v1/users/kycs", "action"=>"webhook", "user_id"=>"#{user.id}")))
      end
    end
  end
end
