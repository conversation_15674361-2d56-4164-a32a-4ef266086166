require 'rails_helper'

RSpec.describe Api::V1::Users::PasswordsController, type: :request do
  let(:confirmed_user) { create(:user_natural, :confirmed) }
  let(:new_password) { 'Password1@123' }
  let(:bad_token) { 'abcdefg' }
  let(:bad_password) { 'a' }

  before(:each) do
    confirmed_user.ensure_reset_token!
  end

  describe 'POST #create' do
    it 'succeeds with valid email' do
      post path, params: { email: confirmed_user.email }

      expect(response).to have_http_status(201)
    end

    it 'throws 422 error when invalid email passed' do
      post path, params: { email: '<EMAIL>' }

      expect(response).to have_http_status(422)
    end

    it 'throws 422 error when no email passed' do
      post path

      expect(response).to have_http_status(422)
    end
  end

  describe 'GET #show' do
    it 'succeeds with valid id and reset token' do
      get path(confirmed_user.id), params: { reset_token: confirmed_user.reset_token }

      expect(response).to have_http_status(200)
    end

    it 'throws 404 error when reset token is invalid' do
      get path(confirmed_user.id), params: { reset_token: bad_token }

      expect(response).to have_http_status(404)
    end

    context 'throws 404 error when reset token is missing' do
      before(:each) do
        confirmed_user.update(reset_token: nil)
      end

      it 'blank string' do
        get path(confirmed_user.id), params: { reset_token: '' }
        expect(response).to have_http_status(404)
      end

      it 'nil' do
        get path(confirmed_user.id), params: { reset_token: nil }
        expect(response).to have_http_status(404)
      end
    end
  end

  describe 'PUT #update' do
    it 'successfully updates the users password and resets their token/session' do
      put path(confirmed_user.id), params: { reset_token: confirmed_user.reset_token, password: new_password }

      expect(response).to have_http_status(200)
      expect(confirmed_user.reload.authenticate(new_password)).not_to eql(false)
      expect(confirmed_user.reload.reset_token).to eql(nil)
      expect(confirmed_user.reload.authentication_token).to eql(nil)
    end

    it 'throws 404 error when reset token is invalid' do
      put path(confirmed_user.id), params: { reset_token: bad_token, password: new_password }

      expect(response).to have_http_status(404)
      expect(confirmed_user.reload.authenticate(new_password)).to eql(false)
    end

    context 'throws 404 error when reset token is missing' do
      before(:each) do
        confirmed_user.update(reset_token: nil)
      end

      it 'blank string' do
        put path(confirmed_user.id), params: { reset_token: '', password: new_password }

        expect(response).to have_http_status(404)
        expect(confirmed_user.reload.authenticate(new_password)).to eql(false)
      end

      it 'nil' do
        put path(confirmed_user.id), params: { reset_token: nil, password: new_password }

        expect(response).to have_http_status(404)
        expect(confirmed_user.reload.authenticate(new_password)).to eql(false)
      end
    end

    it 'validates the users password' do
      put path(confirmed_user.id), params: { reset_token: confirmed_user.reset_token, password: bad_password }

      expect(response).to have_http_status(422)
      expect(confirmed_user.reload.authenticate(new_password)).to eql(false)
    end
  end

  def path(suffix = '')
    "/api/v1/users/passwords/#{suffix}"
  end
end
