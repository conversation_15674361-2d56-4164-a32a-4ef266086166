require 'rails_helper'

RSpec.describe Api::V1::Users::MandatesController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }

  let(:mandate_params) { attributes_for(:mandate) }
  let(:mandate) { create(:mandate, user: user) }

  let(:auth_headers) { auth_headers_for_user(user) }

  describe 'GET #index' do
    it 'should return a list of mandates' do
      # Create mandate
      mandate

      get index_path(user), headers: auth_headers

      expect(response).to have_http_status(200)

      [:id, :user_id, :bank_account_id, :amount, :mangopay_mandate].each do |attribute|
        expect(response_json.first).to have_key(attribute.to_s)
      end
    end
  end

  # describe 'POST #create' do
    # @note: Temp disable this endpoint on Sam's request
    # it 'should return unauthorized' do
    #   post index_path(user), params: mandate_params, headers: auth_headers

    #   expect(response).to have_http_status(401)
    # end

    # it 'should successfully create a mandate' do
    #   post index_path(user), params: mandate_params, headers: auth_headers
    #
    #   expect(response).to have_http_status(201)
    # end
    #
    # it 'should require a positive amount' do
    #   post index_path(user), params: mandate_params.merge(amount: -10.00), headers: auth_headers
    #
    #   expect(response).to have_http_status(422)
    #   expect(response_json['errors']['amount'].first).to eq('must be greater than 0')
    # end
    #
    # it 'should error correctly on failure' do
    #   post index_path(user), headers: auth_headers
    #
    #   expect(response).to have_http_status(422)
    # end
  # end

  describe 'GET #show' do
    it 'should correctly return a mandate' do
      get singular_path(user, mandate.id), headers: auth_headers

      expect(response).to have_http_status(200)
    end

    it 'should error when an invalid id is passed' do
      get singular_path(user, 0), headers: auth_headers

      expect(response).to have_http_status(404)
    end
  end

  describe 'PATCH #update' do
    let(:original_day) { mandate.day }
    let(:new_day) { mandate.day == 1 ? 2 : mandate.day - 1 }
    let(:mandate_params) { { day: new_day } }
    let(:request_example) { patch singular_path(mandate.user, mandate.id), params: mandate_params, headers: auth_headers_for_user(mandate.user) }

    context do
      before { request_example }

      context 'with valid params' do
        it { expect(assigns(:mandate).valid?).to eq(true) }
        it { expect(mandate.reload.day).to eq(new_day) }
        it { expect(response).to have_http_status(200) }
      end

      context 'with invalid params' do
        let(:mandate_params) { { day: 60 } }
        it { expect(assigns(:mandate).valid?).to eq(false) }
        it { expect(mandate.reload.day).to eq(original_day) }
        it { expect(response).to have_http_status(422) }
      end
    end
  end

  describe 'DELETE #cancel' do
    it 'should successfully cancel' do
      delete cancel_path(user, mandate.id), headers: auth_headers

      expect(response).to have_http_status(200)
    end
  end

  def index_path(user)
    "/api/v1/users/#{user.id}/mandates"
  end

  def singular_path(user, id)
    "/api/v1/users/#{user.id}/mandates/#{id}"
  end

  def cancel_path(user, id)
    "/api/v1/users/#{user.id}/mandates/#{id}/cancel"
  end
end
