require 'rails_helper'

RSpec.describe Api::V1::Users::PaymentLogsController, type: :request do
  let(:user_1) { create(:user_natural) }
  let(:user_2) { create(:user_natural) }

  let(:auth_headers) { auth_headers_for_user(user_1) }

  describe 'GET #index' do
    it 'should return all your payment logs and not someone elses' do
      10.times { create(:payment_log, user: user_1) }
      10.times { create(:payment_log, user: user_2) }

      get path(user_1), headers: auth_headers

      expect(PaymentLog.count).to eq(20)
      expect(response_json.size).to eq(10)
    end

    context 'date filtering' do
      let(:start_date) { 3.weeks.ago }
      let(:end_date) { 1.week.ago }
      let(:quantity) { 5 }

      before(:each) do
        quantity.times { create(:payment_log, user: user_1) }
      end

      it 'should accept a date range' do
        create(:payment_log, user: user_1, created_at: 2.weeks.ago)

        get path(user_1), params: { start_date: start_date, end_date: end_date }, headers: auth_headers

        expect(response).to have_http_status(200)
        expect(response_json.size).to eq(1)
        expect(user_1.payment_logs.count).to be > 1
      end

      it 'should error if dates are the wrong order' do
        get path(user_1), params: { start_date: end_date, end_date: start_date }, headers: auth_headers

        expect(response).to have_http_status(422)
      end
    end
  end

  def path(user)
    "/api/v1/users/#{user.id}/payment_logs"
  end
end
