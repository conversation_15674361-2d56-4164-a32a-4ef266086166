require 'rails_helper'

RSpec.describe Api::V1::Users::DashboardTwosController, type: :request do
  let(:user) { create(:user_natural) }

  describe 'GET #show' do
    context 'with valid user ID' do
      it 'succeeds' do
        get path(user.id), headers: auth_headers_for_user(user)

        expect(response).to have_http_status(200)
      end
    end

    context 'with invalid user ID' do
      it 'fails' do
        get path(user.id)

        expect(response).to have_http_status(401)
      end
    end
  end

  def path(id)
    "/api/v1/users/#{id}/dashboard_two"
  end
end
