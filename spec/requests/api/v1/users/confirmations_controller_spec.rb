require 'rails_helper'

RSpec.describe Api::V1::Users::ConfirmationsController, type: :request do
  let(:pending_user) { create(:user_natural) }
  let(:confirmed_user) { create(:user_natural, :confirmed) }
  let(:ip_address) { '***************' }
  let(:auth_headers) { auth_headers_for_user(confirmed_user) }

  describe 'PUT #update' do
    it 'successfully confirms a user' do
      pending_user.ensure_confirmation_token!

      put singular_path(pending_user.id), params: { confirmation_token: pending_user.confirmation_token }

      expect(response).to have_http_status(200)
      expect(response_json['confirmation_token']).to eq(nil)
      expect(response_json['confirmed_at']).to_not be_nil
    end

    it 'fails correctly when invalid params passed' do
      put singular_path(pending_user.id), params: { confirmation_token: 123 }

      expect(response).to have_http_status(404)
    end
  end

  describe 'PUT #resend' do
    it 'succeeds with valid email' do
      put path('resend'), params: { email: pending_user.email }

      expect(response).to have_http_status(200)
    end

    it 'fails with invalid email' do
      put path('resend'), params: { email: '<EMAIL>' }

      expect(response).to have_http_status(404)
    end

    it 'fails for user that is already confirmed' do
      put path('resend'), params: { email: confirmed_user.email }

      expect(response).to have_http_status(404)
    end
  end

  describe 'GET #sca_enroll' do
    context 'when SCA success' do
      before do
        allow(MangoPay).to receive(:request).and_return({ RedirectUrl: 'https://test_sca.mangopay.com' })
      end

      it 'returns a successful response with JSON data' do
        get path('sca_enroll'), params: { user_id: confirmed_user.id }, headers: auth_headers
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({ "RedirectUrl" => "https://test_sca.mangopay.com" })
      end
    end
    context 'when SCA fails' do
      before do
        allow(MangoPay).to receive(:request).and_raise(StandardError.new('mangopay error msg'))
      end
      it 'returns an error response' do
        get path('sca_enroll'), params: { user_id: confirmed_user.id }, headers: auth_headers
        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)).to eq({ 'errors' => { 'base' => ['mangopay error msg'] } })
      end
    end
  end

  describe 'GET #webhook' do
    context 'when webhook succeeded' do
      it 'should update the sca status to true' do
        get path('webhook'), params: { user_id: confirmed_user.id, event_type: 'USER_ACCOUNT_ACTIVATED' }, headers: auth_headers
        expect(confirmed_user.reload.sca_status).to be true
      end
      it 'should send success email to user' do
        get path('webhook'), params: { user_id: confirmed_user.id, event_type: 'USER_ACCOUNT_ACTIVATED' }, headers: auth_headers
        expect(queue_job?('ScaMailer', 'success')).to be true
      end
    end

    context 'when webhook fail' do
      it 'should  not update the sca status to true' do
        get path('webhook'), params: { user_id: confirmed_user.id, actionStatus: 'failed' }, headers: auth_headers
        expect(confirmed_user.reload.sca_status).to be false
      end
      it 'should send success email to user' do
        get path('webhook'), params: { user_id: confirmed_user.id, actionStatus: 'failed' }, headers: auth_headers

        expect(queue_job?('ScaMailer', 'failure')).to be true
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  def path(suffix = '')
    "/api/v1/users/confirmations/#{suffix}"
  end

  def singular_path(id)
    "/api/v1/users/confirmations/#{id}"
  end
end
