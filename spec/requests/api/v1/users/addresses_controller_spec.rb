require 'rails_helper'

RSpec.describe Api::V1::Users::AddressesController, type: :request do
  let(:user_address) { create(:user_address, :personal) }
  let(:user) { user_address.user }
  let(:auth_headers) { auth_headers_for_user(user) }

  describe 'POST #create' do
    let(:natural_user) { create(:user_natural) }
    let(:auth_headers) { auth_headers_for_user(natural_user) }
    context 'when address create successfully' do
      before do
        post api_v1_user_addresses_url(user_id: natural_user.id), params: {
          address_number: '48',
          address_1: 'Golf Road',
          address_2: '813 Howard Street',
          city: 'Swerford',
          region: 'LONDON',
          post_code: 'OX7 3RX',
          country: 'GB'
        }, headers: auth_headers
        @address = natural_user.reload.address
      end

      it 'should create address with address number to 49' do
        expect(@address.address_number).to eq '48'
      end

      it 'should create address with address_1 to "Golf Road"' do
        expect(@address.address_1).to eq 'Golf Road'
      end

      it 'should create address with address_2 to "813 Howard Street"' do
        expect(@address.address_2).to eq '813 Howard Street'
      end

      it 'should create address with city to "Swerford"' do
        expect(@address.city).to eq 'Swerford'
      end

      it 'should create address with region to "LONDON"' do
        expect(@address.region).to eq 'LONDON'
      end

      it 'should create address with post_code to "OX7 3RX"' do
        expect(@address.post_code).to eq 'OX7 3RX'
      end

      it 'should create address with country to "GB"' do
        expect(@address.country).to eq 'GB'
      end

      it 'should send email to user' do
        expect(queue_job?('UserAddressMailer','change_address_notification')).to be true
      end

      it 'should notify to admin' do
        expect(ChangeAddressNotifier.count).to eq 1
      end

      it 'recipient shoud be current user' do
        expect(ChangeAddressNotifier.first.record_id).to eq natural_user.id
        expect(ChangeAddressNotifier.first.record_type).to eq 'User'
      end
    end
    context 'when address failed to create' do
      before do
        post api_v1_user_addresses_url(user_id: natural_user.id), params: {
          address_number: '48',
          address_1: '',
          address_2: '',
          city: 'Swerford',
          region: 'LONDON',
          post_code: 'OX7 3RX',
          country: 'GB'
        }, headers: auth_headers
        @address = natural_user.reload.address
      end
      it 'should have error message' do
        expect(response_json['errors']['address_1']).to eq ["can't be blank"]
      end
    end
  end

  describe 'PATCH #update' do
    context 'when address update successfully' do
      before do
        put singular_path(user.id, user_address.id),
            headers: auth_headers,
            params: {
              address_number: '49',
              address_1: '49 Featherstone Street',
              address_2: '813 Howard Street',
              city: 'Oswego',
              region: 'LONDON',
              post_code: 'EC1Y 8SY',
              country: 'GB'
            }
        @address = user.address.reload
      end

      it 'should update address number to 49' do
        expect(@address.address_number).to eq '49'
      end

      it 'should update address_1 to "49 Featherstone Street"' do
        expect(@address.address_1).to eq '49 Featherstone Street'
      end

      it 'should update address_2 to "813 Howard Street"' do
        expect(@address.address_2).to eq '813 Howard Street'
      end

      it 'should update city to "Oswego"' do
        expect(@address.city).to eq 'Oswego'
      end

      it 'should update region to "LONDON"' do
        expect(@address.region).to eq 'LONDON'
      end

      it 'should update post_code to "EC1Y 8SY"' do
        expect(@address.post_code).to eq 'EC1Y 8SY'
      end

      it 'should update country to "GB"' do
        expect(@address.country).to eq 'GB'
      end

      it 'should send email to user' do
        expect(queue_job?('UserAddressMailer','change_address_notification')).to be true
      end

      it 'should notify to admin' do
        expect(ChangeAddressNotifier.count).to eq 1
      end

      it 'recipient shoud be current user' do
        expect(ChangeAddressNotifier.first.record_id).to eq user.id
        expect(ChangeAddressNotifier.first.record_type).to eq 'User'
      end
    end

    context 'when address update fail' do
      before do
        put singular_path(user.id, user_address.id),
            headers: auth_headers,
            params: {
              address_number: '49',
              address_1: '',
              address_2: '813 Howard Street',
              city: 'Oswego',
              region: 'LONDON',
              post_code: 'EC1Y 8SY',
              country: 'GB'
            }
      end

      it 'should have error message' do
        expect(response_json['errors']['address_1']).to eq ["can't be blank"]
      end

      it 'should not notify to admin' do
        expect(ChangeAddressNotifier.count).to eq 0
      end
    end
  end

  def singular_path(user_id, id)
    "/api/v1/users/#{user_id}/addresses/#{id}"
  end
end
