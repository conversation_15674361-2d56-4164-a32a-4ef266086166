# require 'rails_helper'
#
# RSpec.describe Api::V1::Users::CertificationsController, type: :request do
#   let(:kyc_light_complete_user) { create(:user_natural, :confirmed, :kyc_light_complete) }
#   let(:self_certified_user) { create(:user_natural, :confirmed, :kyc_light_complete, :self_certified) }
#   let(:certification_level_id) { create(:certification_level).id }
#
#   describe 'POST #create' do
#     it 'accepts a valid certification_level_id' do
#       post singular_path(kyc_light_complete_user.id), params: { certification_level_id: certification_level_id,
#                                                                 authentication_token: kyc_light_complete_user.authentication_token },
#                                                       headers: auth_headers_for_user(kyc_light_complete_user)
#
#       expect(response).to have_http_status(201)
#       expect(response_json['certification_level_id']).to eq(certification_level_id)
#       expect(response_json['aasm_state']).to eq('self_certified')
#       expect(kyc_light_complete_user.certification_attempts.where(state: 'success').count).to eq(1)
#     end
#
#     it 'rejects an invalid valid certification_level_id' do
#       post singular_path(kyc_light_complete_user.id), params: { certification_level_id: 123,
#                                                                 authentication_token: kyc_light_complete_user.authentication_token },
#                                                       headers: auth_headers_for_user(kyc_light_complete_user)
#
#       expect(response).to have_http_status(422)
#       expect(response_json['errors']['certification_level'].size).to be > 0
#     end
#
#     it 'rejects any other params' do
#       post singular_path(kyc_light_complete_user.id), params: { certification_level_id: certification_level_id,
#                                                                 first_name: 'Boris',
#                                                                 authentication_token: kyc_light_complete_user.authentication_token },
#                                                       headers: auth_headers_for_user(kyc_light_complete_user)
#
#       expect(response).to have_http_status(201)
#       expect(response_json['first_name']).not_to eq('Boris')
#     end
#
#     it 'accepts a recertifying user and reverts state' do
#       state_before = self_certified_user.aasm_state
#
#       expect(self_certified_user.recertify!).to eq(true)
#
#       post singular_path(self_certified_user.id), params: { certification_level_id: certification_level_id,
#                                                             authentication_token: self_certified_user.authentication_token },
#                                                   headers: auth_headers_for_user(self_certified_user)
#
#       expect(response).to have_http_status(201)
#       expect(response_json['certification_level_id']).to eq(certification_level_id)
#       expect(response_json['aasm_state']).to eq(state_before)
#     end
#
#     context 'when a recertifying user' do
#       let(:user_1) { create(:user_natural, :confirmed, :kyc_light_complete, :self_certified) }
#       let(:user_2) { create(:user_natural, :confirmed, :kyc_light_complete, :self_certified, :kyc_regular_required) }
#       let(:user_3) { create(:user_natural, :confirmed, :kyc_light_complete, :self_certified, :kyc_regular_submitted) }
#       let(:user_4) { create(:user_natural, :confirmed, :kyc_light_complete, :self_certified, :kyc_regular_submitted, :kyc_regular_complete) }
#       let(:user_5) { create(:user_natural, :confirmed, :kyc_light_complete, :self_certified, :kyc_regular_submitted, :kyc_regular_failed) }
#       let(:users) { [user_1, user_2, user_3, user_4, user_5] }
#
#       it 'allows the transition and sets the state back' do
#         users.each do |user|
#           state_before = user.aasm_state
#           state_count_before = User::State.count
#
#           post singular_path(user.id), params: { certification_level_id: certification_level_id,
#                                                  authentication_token: user.authentication_token },
#                                        headers: auth_headers_for_user(user)
#
#           expect(response).to have_http_status(201)
#           expect(response_json['certification_level_id']).to eq(certification_level_id)
#           expect(response_json['aasm_state']).to eq(state_before)
#           expect(User::State.count).to eq(state_count_before + 2)
#         end
#       end
#     end
#   end
#
#   describe 'PUT #failed' do
#     let(:request) do
#       put "#{singular_path(kyc_light_complete_user.id)}/failed", params: { certification_level_id: certification_level_id,
#                                                                            authentication_token: kyc_light_complete_user.authentication_token },
#                                                                  headers: auth_headers_for_user(kyc_light_complete_user)
#     end
#
#     before do
#       request
#     end
#
#     it 'returns a 201' do
#       expect(response).to have_http_status(:created)
#     end
#
#     it 'doesnt change the users certification level id' do
#       expect(response_json['certification_level_id']).to eq(nil)
#     end
#
#     it 'changes the users state' do
#       expect(response_json['aasm_state']).to eq('first_self_certification_failed')
#     end
#
#     it 'logs the failure' do
#       expect(kyc_light_complete_user.certification_attempts.where(state: 'fail').count).to eq(1)
#     end
#   end
#
#   def singular_path(id)
#     "/api/v1/users/#{id}/certifications"
#   end
# end
