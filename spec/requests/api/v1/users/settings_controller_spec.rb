require 'rails_helper'

RSpec.describe Api::V1::Users::SettingsController, type: :request do
  let(:old_password) { 'Password123@' }
  let(:new_password) { 'MyNewPassword123' }
  let(:password_confirmation) { 'MyNewPassword123' }
  let(:address) { create(:user_address, :personal) }
  let(:user) { address.user }

  let(:auth_headers) { auth_headers_for_user(user) }

  describe 'POST #create' do
    it 'successfully update user data' do

      post api_v1_user_settings_url(user_id: user.id), params: { phone_number: '+923014965032', "users_address" => { "id" => user.address.id, "address_number" => "49", "address_1" => "	Featherstone Street", "address_2" => "", "city" => "Leeds", "region" => "Roundhay", "country" => "GB", "post_code" => "LS8 2ED" } }, headers: auth_headers

      expect(response).to have_http_status(200)
    end

    it 'failed update user data' do

      post api_v1_user_settings_url(user_id: user.id), params: { phone_number: '+923014965032', "users_address" => { "id" => user.address.id, "address_number" => "49", "address_1" => "	wrong address", "address_2" => "", "city" => "Leeds", "region" => "Roundhay", "country" => "GB", "post_code" => "wrong post code" } }, headers: auth_headers

      expect(response).to have_http_status(422)
    end

    it 'failed update user data when phone is not correct' do

      post api_v1_user_settings_url(user_id: user.id), params: { phone_number: '123456', "users_address" => { "id" => user.address.id, "address_number" => "49", "address_1" => "	wrong address", "address_2" => "", "city" => "Leeds", "region" => "Roundhay", "country" => "GB", "post_code" => "wrong post code" } }, headers: auth_headers

      expect(response).to have_http_status(422)
    end

    it 'fails to update password when old password not sent' do
      post singular_path(user), params: { password: new_password }, headers: auth_headers

      expect(response).to have_http_status(422)
      expect(response_json['errors']['old_password'].size).to be > 0
      expect(response_json['errors']['old_password'].first).to eq('is incorrect')
    end

    it 'fails to update password when wrong old password not sent' do
      post singular_path(user), params: { password: new_password, old_password: 'password' }, headers: auth_headers

      expect(response).to have_http_status(422)
      expect(response_json['errors']['old_password'].size).to be > 0
      expect(response_json['errors']['old_password'].first).to eq('is incorrect')
    end
  end

  describe 'POST #update email' do
    context 'when password is wrong' do
      it 'should reurn 422 unprocessable error' do
        post update_email_api_v1_user_settings_url(user_id: user.id), params: { password: 'wrong password', email: '<EMAIL>' }, headers: auth_headers
        expect(response).to have_http_status(422)
      end
      it 'should return error message' do
        post update_email_api_v1_user_settings_url(user_id: user.id), params: { password: 'wrong password', email: '<EMAIL>' }, headers: auth_headers
        expect(JSON.parse(response.body)['error']).to eq ('Password is not correct.')
      end
    end

    context 'when email is wrong' do
      it 'should reurn 422 unprocessable error' do
        post update_email_api_v1_user_settings_url(user_id: user.id), params: { password: user.password, email: '' }, headers: auth_headers
        expect(response).to have_http_status(422)
      end
      it 'should return error message' do
        post update_email_api_v1_user_settings_url(user_id: user.id), params: { password: user.password, email: '' }, headers: auth_headers
        expect(JSON.parse(response.body)['error']).to eq ('Invalid email address.')
      end
    end

    context 'when successfully email updated' do
      it 'should update the email' do
        post update_email_api_v1_user_settings_url(user_id: user.id), params: { password: user.password, email: '<EMAIL>' }, headers: auth_headers
        expect(user.reload.email).to eq '<EMAIL>'
      end

      it 'should set confirm at nil' do
        post update_email_api_v1_user_settings_url(user_id: user.id), params: { password: user.password, email: '<EMAIL>' }, headers: auth_headers
        expect(user.reload.confirmed_at).to eq nil
      end

      it 'should response with message' do
        post update_email_api_v1_user_settings_url(user_id: user.id), params: { password: user.password, email: '<EMAIL>' }, headers: auth_headers
        expect(JSON.parse(response.body)['message']).to eq 'Your email has been updated. please check your inbox to confirm email.'
      end

    end

  end

  describe 'POST #update password' do
    context 'when password is not correct' do
      it 'should not update the password' do
        post update_password_api_v1_user_settings_url(user_id: user.id), params: { user_id: user.id, password: '1', old_password: user.password, password_confirmation: 'Uown@123Test' }, headers: auth_headers
        expect(response).to have_http_status(422)
      end

      it 'faild to update password with incorrect OTP' do
        user.update(factor_id: 'factor_id_1234')
        allow(Twilio::Totp::Challenge).to receive(:new).and_return(double(call: { success: false, error: 'some thing wrong, please try again.' }))

        post update_password_api_v1_user_settings_url(user_id: user.id), params: { user_id: user.id, password: 'Uown@123Test', old_password: user.password, password_confirmation: 'Uown@123Test' }, headers: auth_headers
        expect(response).to have_http_status(422)
      end
    end
    context 'when password is correct' do
      it 'should update the password' do
        user.update(factor_id: 'factor_id_1234')
        allow(Twilio::Totp::Challenge).to receive(:new).and_return(double(call: { success: true, data: double(status: 'approved') }))

        post update_password_api_v1_user_settings_url(user_id: user.id), params: { user_id: user.id, password: 'Uown@123Test', old_password: user.password, password_confirmation: 'Uown@123Test' }, headers: auth_headers
        expect(response).to have_http_status(200)
      end
    end
  end

  def singular_path(user)
    "/api/v1/users/#{user.id}/settings/update_password"
  end
end
