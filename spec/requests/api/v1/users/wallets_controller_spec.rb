require 'rails_helper'

RSpec.describe Api::V1::Users::WalletsController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:confirmed_user) { create(:user_natural, :confirmed) }

  describe 'GET #index' do
    it 'correctly returns a list of wallets' do
      get path(user), headers: auth_headers_for_user(user)

      expect(response).to have_http_status(200)
    end

    it 'returns an empty array for a non mangopay user' do
      get path(confirmed_user), headers: auth_headers_for_user(confirmed_user)

      expect(response).to have_http_status(200)
      expect(confirmed_user.mangopay_id).to eq(nil)
      expect(response_json.size).to eq(0)
    end
  end

  def path(user)
    "/api/v1/users/#{user.id}/wallets"
  end
end
