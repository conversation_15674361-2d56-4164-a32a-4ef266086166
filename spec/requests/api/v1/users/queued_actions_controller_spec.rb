require 'rails_helper'

RSpec.describe Api::V1::Users::QueuedActionsController, type: :request do
  let(:user_1) { create(:user_natural) }
  let(:user_2) { create(:user_natural) }

  describe 'GET #index' do
    it 'should return all your active queued actions and not someone elses' do
      create_list(:user_queued_action_investment, 3, user: user_1)
      create_list(:user_queued_action_payout, 3, user: user_1)
      create_list(:user_queued_action, 3, user: user_2)

      get path(user_1), headers: auth_headers_for_user(user_1)

      expect(User::QueuedAction.count).to eq(9)
      expect(response_json.size).to eq(6)
    end
  end

  def path(user)
    "/api/v1/users/#{user.id}/queued_actions"
  end
end
