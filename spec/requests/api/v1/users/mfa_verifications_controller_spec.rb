require 'rails_helper'

RSpec.describe Api::V1::Users::PhoneVerificationsController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:auth_headers) { auth_headers_for_user(user) }

  describe 'POST #create' do
    context 'when successfully register user as twilio entity' do
      it 'should response with QR uri' do
        allow(Twilio::Entity::Create).to receive(:new).and_return(double(call: { success: true }))
        allow(Twilio::Totp::Create).to receive(:new).and_return(double(call: { success: true, 'data': double(binding: { secret: "xyz", uri: 'otpauth://totp/rspec:rails?secret=xyz' }) }))

        post api_v1_users_mfa_verifications_url, headers: auth_headers, params: { user_id: user.id }

        expect(json_response['uri']).to eq 'otpauth://totp/rspec:rails?secret=xyz'
      end

      it 'should response with secret code' do
        allow(Twilio::Entity::Create).to receive(:new).and_return(double(call: { success: true }))
        allow(Twilio::Totp::Create).to receive(:new).and_return(double(call: { success: true, 'data': double(binding: { secret: "xyz", uri: 'otpauth://totp/rspec:rails?secret=xyz' }) }))

        post api_v1_users_mfa_verifications_url, headers: auth_headers, params: { user_id: user.id }

        expect(json_response['secret']).to eq 'xyz'
      end

    end
    context 'when twillio entity api raise error' do
      it 'should response with error' do
        allow(Twilio::Entity::Create).to receive(:new).and_return(double(call: { success: false, error: 'some thing wrong. please try again.' }))

        post api_v1_users_mfa_verifications_url, headers: auth_headers, params: { user_id: user.id }
        expect(json_response['errors']['base']).to eq ["some thing wrong. please try again."]
      end
    end
  end
  describe 'GET #verify_otp' do
    context 'when the OTP is valid' do
      it 'should response with verified' do
        allow(Twilio::Entity::Show).to receive(:new).and_return(double(call: { data: double(factors: double(list: double(last: double(sid: '123456')))) }))
        allow(Twilio::Totp::Verify).to receive(:new).and_return(double(call: { success: true, data: double(status: 'verified') }))

        get verify_otp_api_v1_users_mfa_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: '123456' }

        expect(json_response).to eq 'verified'
      end

      it 'should update the factor id' do
        allow(Twilio::Entity::Show).to receive(:new).and_return(double(call: { data: double(factors: double(list: double(last: double(sid: '123456')))) }))
        allow(Twilio::Totp::Verify).to receive(:new).and_return(double(call: { success: true, data: double(status: 'verified') }))

        get verify_otp_api_v1_users_mfa_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: '123456' }

        expect(user.reload.factor_id).to eq '123456'
      end
    end
    context 'when twillio has error' do
      it 'should response with error' do
        allow(Twilio::Entity::Show).to receive(:new).and_raise(Exception.new('some thing wrong, please try again.'))
        get verify_otp_api_v1_users_mfa_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: '123456' }
        expect(json_response['errors']['base']).to eq ["some thing wrong, please try again."]
      end
    end
  end
  describe 'GET #challenge' do
    context 'when the OTP is valid' do
      it 'should response with verified' do
        allow(Twilio::Totp::Challenge).to receive(:new).and_return(double(call: { success: true, data: double(status: 'approved') }))

        get challenge_api_v1_users_mfa_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: '123456' }

        expect(json_response).to eq 'verified'
      end
    end

    context 'when twillio has error' do
      it 'should response with error' do
        allow(Twilio::Totp::Challenge).to receive(:new).and_raise(Exception.new('some thing wrong, please try again.'))
        get challenge_api_v1_users_mfa_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: '123456' }
        expect(json_response['errors']['base']).to eq ["some thing wrong, please try again."]
      end

      it 'should response with error' do
        allow(Twilio::Totp::Challenge).to receive(:new).and_return(double(call: { success: false, error: 'some thing wrong, please try again.' }))
        get challenge_api_v1_users_mfa_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: '123456' }
        expect(json_response['errors']['base']).to eq ["some thing wrong, please try again."]
      end
    end

    context 'when otp invalid' do
      it 'should response with error' do
        allow(Twilio::Totp::Challenge).to receive(:new).and_return(double(call: { success: true, data: double(status: 'not approved') }))
        get challenge_api_v1_users_mfa_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: '123456' }
        expect(json_response['errors']['base']).to eq ["Invalid OTP"]
      end
    end
  end

  describe 'GET #cancel' do
    context 'when the OTP is valid' do
      it 'should remove factor id of user' do
        user.update(factor_id: '123456')
        allow(Twilio::Totp::Challenge).to receive(:new).and_return(double(call: { success: true, data: double(status: 'approved') }))
        allow(::Twilio::Entity::Delete).to receive(:new).and_return(double(call: { success: true }))
        delete cancel_api_v1_users_mfa_verification_url(user.id), headers: auth_headers, params: { user_id: user.id, otp: '123456' }
        expect(user.reload.factor_id).to eq nil
      end

      it 'should response with mfa removed text' do
        user.update(factor_id: '123456')
        allow(Twilio::Totp::Challenge).to receive(:new).and_return(double(call: { success: true, data: double(status: 'approved') }))
        allow(::Twilio::Entity::Delete).to receive(:new).and_return(double(call: { success: true }))
        delete cancel_api_v1_users_mfa_verification_url(user.id), headers: auth_headers, params: { user_id: user.id, otp: '123456' }
        expect(json_response).to eq 'MFA has been removed'
      end
    end
    context 'when twillio has error' do
      it 'should response with error' do
        allow(Twilio::Totp::Challenge).to receive(:new).and_raise(Exception.new('some thing wrong, please try again.'))
        delete cancel_api_v1_users_mfa_verification_url(user.id), headers: auth_headers, params: { user_id: user.id, otp: '123456' }
        expect(json_response['errors']['base']).to eq ["some thing wrong, please try again."]
      end
    end
  end

  private

  def json_response
    JSON.parse(response.body)
  end
end
