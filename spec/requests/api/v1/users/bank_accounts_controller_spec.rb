require 'rails_helper'

RSpec.describe Api::V1::Users::BankAccountsController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:bank_account_params) { attributes_for(:mango_bank_account) }

  let(:auth_headers) { auth_headers_for_user(user) }

  describe 'GET #index' do
    it 'should return a list of bank accounts' do
      get index_path(user), headers: auth_headers

      expect(response).to have_http_status(200)

      [:id, :address_1, :address_2, :city, :region, :post_code,
       :country, :name, :sort_code, :account_number, :active].each do |attribute|
        expect(response_json.first).to have_key(attribute.to_s)
      end
    end
  end

  describe 'POST #create' do
    before do
      allow(Mango::BankAccount).to receive(:allow_new_account?).with(user).and_return(true)
    end

    it 'should successfully create a bank_account' do
      post index_path(user), params: bank_account_params, headers: auth_headers

      expect(response).to have_http_status(201)
    end

    it 'should error correctly on failure' do
      post index_path(user), params: {}, headers: auth_headers

      expect(response).to have_http_status(422)
    end
  end

  describe 'POST #create' do
    before do
      allow(Mango::BankAccount).to receive(:allow_new_account?).with(user).and_return(false)
    end

    it 'should block user to create bank account' do
      post index_path(user), params: bank_account_params, headers: auth_headers

      expect(response).to have_http_status(422)
      expect(JSON.parse(response.body)).to eq ({"errors"=>{"base"=>["You are not authorized to perform this action."]}})
    end
  end

  describe 'GET #show' do
    it 'should correctly return a bank account' do
      get singular_path(user, bank_account_params[:id]), headers: auth_headers

      expect(response).to have_http_status(200)
    end

    it 'should error when an invalid id is passed' do
      get singular_path(user, 0), headers: auth_headers

      expect(response).to have_http_status(404)
    end
  end

  def index_path(user)
    "/api/v1/users/#{user.id}/bank_accounts"
  end

  def singular_path(user, id)
    "/api/v1/users/#{user.id}/bank_accounts/#{id}"
  end
end
