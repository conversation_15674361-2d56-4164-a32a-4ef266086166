require 'rails_helper'

RSpec.describe Api::V1::Users::RegistrationsController, type: :request do
  let(:path) { '/api/v1/users/registrations' }
  let(:new_user_params) { attributes_for(:user_natural) }
  let(:new_legal_user_params) { attributes_for(:user_legal) }

  describe 'POST #create' do
    context 'natural' do
      it 'succeeds with valid email' do
        post path, params: new_user_params

        expect(response).to have_http_status(201)
        expect(assigns(:user).confirmation_token).not_to eq(nil)
      end
    end

    context 'legal' do
      it 'succeeds with valid email' do
        post path, params: new_legal_user_params.merge(type: 'legal')

        expect(response).to have_http_status(201)
        expect(assigns(:user).confirmation_token).not_to eq(nil)
        expect(assigns(:user).class).to eq(User::Legal)
      end
    end

    it 'fails with invalid email' do
      post path, params: new_user_params.merge(email: 'bogus')

      expect(response).to have_http_status(422)
    end

    it 'requires password confirmation' do
      new_user_params[:password_confirmation] = 'testing'

      post path, params: new_user_params

      expect(response).to have_http_status(422)
      expect(response_json['errors']['password_confirmation'].size).to be > 0
      expect(response_json['errors']['password_confirmation'].first).to eq("Passwords don't match")
    end

    it 'fails with no password passed' do
      post path, params: { email: '<EMAIL>' }

      expect(response).to have_http_status(422)
      expect(response_json['errors']['password'].size).to be > 0
    end

    it 'fails gracefully with no params passed' do
      post path

      expect(response).to have_http_status(422)
    end
  end
end
