require 'rails_helper'

RSpec.describe Api::V1::Users::SessionsController, type: :request do
  let(:valid_user) { create(:user_natural, :confirmed) }
  let(:ip_address) { '***************' }

  describe 'POST #create' do
    it 'succeeds with valid details' do
      post path, params: { email: valid_user.email, password: valid_user.password }

      expect(response).to have_http_status(201)
      expect(response_json['authentication_token']).not_to eq(nil)
    end

    it 'succeeds with valid otp' do
      valid_user.update(factor_id: '12345')
      allow(Twilio::Totp::Challenge).to receive(:new).and_return(double(call: { success: true, data: double({ status: 'approved'}) }))

      post path, params: { email: valid_user.email, password: valid_user.password, otp: '12345' }

      expect(response).to have_http_status(201)
    end

    it 'raise a flag if more than 20 login attempts are made within a week' do
      create_list(:user_login_attempt, 21, user: valid_user)
      post path, params: { email: valid_user.email, password: valid_user.password }

      expect(LoginAttemptNotifier.count).to eq 1
    end

    it 'fails with locked user' do
      valid_user.update(locked_at: Time.now)
      post path, params: { email: valid_user.email, password: valid_user.password }

      expect(response).to have_http_status(423)
    end

    it 'fails with blank otp' do
      valid_user.update(factor_id: '12345')
      post path, params: { email: valid_user.email, password: valid_user.password }

      expect(response).to have_http_status(406)
    end

    it 'fails with invalid otp' do
      valid_user.update(factor_id: '12345')
      allow(Twilio::Totp::Challenge).to receive(:new).and_return(double(call: { success: true, data: double({ status: 'not approve'}) }))

      post path, params: { email: valid_user.email, password: valid_user.password, otp: '12345' }

      expect(response).to have_http_status(406)
    end

    it 'fails with invalid details' do
      post path, params: { email: 'bogus', password: 'bogus' }

      expect(response).to have_http_status(422)
    end

    it 'fails with valid email and wrong password' do
      post path, params: { email: valid_user.email, password: 'bogus' }

      expect(response).to have_http_status(422)
    end

    it 'fails gracefully with no params passed' do
      post path

      expect(response).to have_http_status(422)
    end

    it 'logs the users ip address' do
      post path, params: { email: valid_user.email, password: valid_user.password, ip_address: ip_address }

      expect(response).to have_http_status(201)
      expect(assigns(:user).current_sign_in_ip).to eq(ip_address)
    end

    it 'alert the new country login' do
      create(:user_login_attempt, user: valid_user)
      allow(::Geocoder).to receive(:search).and_return([double(country: 'United Kingdom')])
      post path, params: { email: valid_user.email, password: valid_user.password, ip_address: ip_address }

      expect(queue_job?('UserLoginMailer', 'new_login_notification')).to be true
    end

    it 'alert when IP threshold limit reach' do
      ['*******','*******','*******','*******','*******','*******','*******','*******','*******','*******','*******'].each do |ip|
        create(:user_login_attempt, user: valid_user, ip: ip)
      end
      allow(::Geocoder).to receive(:search).and_return([double(country: 'United Kingdom')])
      post path, params: { email: valid_user.email, password: valid_user.password, ip_address: ip_address }

      expect(LoginAttemptNotifier.count).to eq 1
    end

    it 'alert when three time login attempts fail' do
      valid_user.update(failed_attempts: 3)
      post path, params: { email: valid_user.email, password: '0000', ip_address: ip_address }
      expect(queue_job?('UserLoginMailer', 'failed_attempts_notification')).to be true
      expect(LoginAttemptNotifier.count).to eq 1
    end

    it 'allow user to login with apple uid' do
      valid_user.update(apple_uid: '123456')
      allow(Omni::AppleTokenValidator).to receive(:new).and_return(double(call: true))
      post path, params: { apple_uid: '123456' }

      expect(response).to have_http_status(201)
    end

    it 'allow user to login with google uid' do
      valid_user.update(google_uid: '123456')
      allow(Omni::GoogleTokenValidator).to receive(:new).and_return(double(call: true))
      post path, params: {google_uid: '123456' }

      expect(response).to have_http_status(201)
    end
  end

  describe 'GET #show' do
    it 'returns the user if valid details are passed' do
      expect(valid_user.authentication_token).not_to eq(nil)

      get singular_path(valid_user.id), headers: auth_headers_for_user(valid_user)

      expect(response).to have_http_status(200)
    end

    it 'correctly errors when bad a authentication token is passed' do
      get singular_path(valid_user.id), headers: { 'Authorization' => 'Token 123' }

      expect(response).to have_http_status(401)
    end
  end

  describe 'delete #destroy' do
    it 'removes the users authentication_token' do
      expect(valid_user.authentication_token).not_to eq(nil)

      delete singular_path(valid_user.id), headers: auth_headers_for_user(valid_user)

      expect(response).to have_http_status(200)
      expect(valid_user.reload.authentication_token).to eq(nil)
    end

    it 'correctly errors when bad a authentication token is passed' do
      delete singular_path(valid_user.id), headers: { 'Authorization' => 'Token 123' }

      expect(response).to have_http_status(401)
    end
  end

  def path
    '/api/v1/users/sessions'
  end

  def singular_path(id)
    "/api/v1/users/sessions/#{id}"
  end
end
