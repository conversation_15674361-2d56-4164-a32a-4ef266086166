require 'rails_helper'

RSpec.describe Api::V1::Users::KycsController, type: :request do
  let(:user) { create(:user_natural) }
  let(:params) do
    {
      email_body: 'Test email body text.',
      email_subject: 'Test Email Subject',
      authentication_token: user.authentication_token
    }
  end

  let(:auth_headers) { auth_headers_for_user(user) }

  describe 'POST #create' do
    it 'returns ok when a valid email sent' do
      post path(user.id), params: params, headers: auth_headers

      expect(response).to have_http_status(200)
    end
  end

  def path(user_id)
    "/api/v1/users/#{user_id}/contacts"
  end
end
