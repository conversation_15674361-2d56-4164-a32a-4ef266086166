require 'rails_helper'

RSpec.describe Api::V1::Users::PhoneVerificationsController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted, :kyc_regular_complete) }
  let(:auth_headers) { auth_headers_for_user(user) }

  describe 'POST #create' do
    context 'when phone number is valid' do
      it 'should response with user' do
        allow(Telnyx::SendOtp).to receive(:new).and_return(double(call: double(success?: true, body: { 'data' => { 'id' => 'telnyx_123' } }.to_json)))

        post api_v1_users_phone_verifications_url, headers: auth_headers, params: { user_id: user.id, phone_number: '923014965032' }
        expect(json_response['id']).to eq user.id
      end

      it 'should update the user phone' do
        allow(Telnyx::SendOtp).to receive(:new).and_return(double(call: double(success?: true, body: { 'data' => { 'id' => 'telnyx_123' } }.to_json)))

        post api_v1_users_phone_verifications_url, headers: auth_headers, params: { user_id: user.id, phone_number: '923014965032' }
        expect(user.reload.phone_number).to eq ('923014965032')

      end

      it 'should update the telnyx id' do
        allow(Telnyx::SendOtp).to receive(:new).and_return(double(call: double(success?: true, body: { 'data' => { 'id' => 'telnyx_123' } }.to_json)))

        post api_v1_users_phone_verifications_url, headers: auth_headers, params: { user_id: user.id, phone_number: '923014965032' }
        expect(user.reload.telnyx_id).to eq 'telnyx_123'
      end
    end
    context 'when phone number is in valid' do
      it 'should response with error' do
        allow(Telnyx::SendOtp).to receive(:new).and_return(double(call: double(success?: true, body: { 'data' => { 'id' => 'telnyx_123' } }.to_json)))

        post api_v1_users_phone_verifications_url, headers: auth_headers, params: { user_id: user.id, phone_number: '0000000' }
        expect(json_response['errors']['base']).to eq ["Invalid phone number."]
      end
    end
    context 'when telnyx has error' do
      it 'should response with error' do
        allow(Telnyx::SendOtp).to receive(:new).and_return(double(call: double(success?: false, body: { 'errors' => [{ 'detail' => 'telnyx unable to process this phone number.' }] }.to_json)))

        post api_v1_users_phone_verifications_url, headers: auth_headers, params: { user_id: user.id, phone_number: '+923014965032' }
        expect(json_response['errors']['base']).to eq ["telnyx unable to process this phone number."]
      end
    end
  end
  describe 'GET #verify_otp' do
    context 'when the OTP is valid' do
      it 'should response with user' do
        allow(Telnyx::AcceptOtp).to receive(:new).and_return(double(call: double(success?: true, body: { 'data' => { 'response_code' => 'accepted' } }.to_json)))

        get verify_otp_api_v1_users_phone_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: '123456' }
        expect(json_response['id']).to eq user.id
      end

      it 'should update the user phone verified to true' do
        allow(Telnyx::AcceptOtp).to receive(:new).and_return(double(call: double(success?: true, body: { 'data' => { 'response_code' => 'accepted' } }.to_json)))

        get verify_otp_api_v1_users_phone_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: '123456' }
        expect(user.reload.phone_verified).to eq (true)
      end
    end
    context 'when telnyx has error' do
      it 'should response with error' do
        allow(Telnyx::AcceptOtp).to receive(:new).and_return(double(call: double(success?: false, body: { 'errors' => [{ 'detail' => 'some thing wrong.please try again.' }] }.to_json)))

        get verify_otp_api_v1_users_phone_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: '123456' }
        expect(json_response['errors']['base']).to eq ["some thing wrong.please try again."]
      end
    end
    context 'when OTP is invalid' do
      it 'should response with error' do
        allow(Telnyx::AcceptOtp).to receive(:new).and_return(double(call: double(success?: true, body: { 'data' => { 'response_code' => 'rejected' } }.to_json)))

        get verify_otp_api_v1_users_phone_verifications_url, headers: auth_headers, params: { user_id: user.id, otp: 'xxxxxx' }
        expect(json_response['errors']['base']).to eq ["Invalid code please check and try again."]
      end
    end
  end

  private

  def json_response
    JSON.parse(response.body)
  end
end
