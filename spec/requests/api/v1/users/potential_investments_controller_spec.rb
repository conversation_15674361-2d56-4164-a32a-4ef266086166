require 'rails_helper'

RSpec.describe Api::V1::Users::PotentialInvestmentsController, type: :request do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:property) { create(:property_regular) }
  let(:quantity) { 2 }
  let(:potential_investment_params) { attributes_for(:potential_investment) }
  let(:item_params) { { property_id: property.id, quantity: quantity } }
  let(:potential_investment) { create(:potential_investment, user: user) }

  let(:auth_headers) { auth_headers_for_user(user) }

  describe 'POST #create' do
    it 'should successfully create a potential investment' do
      post index_path(user), params: potential_investment_params.merge(items_attributes: [item_params]), headers: auth_headers

      expect(response).to have_http_status(201)
    end

    it 'should error correctly on failure' do
      post index_path(user), params: potential_investment_params.merge(items_attributes: [{ quantity: quantity }]), headers: auth_headers

      expect(response).to have_http_status(422)
    end
  end

  describe 'GET #show' do
    it 'should correctly return a potential investment' do
      get singular_path(user, potential_investment.id), headers: auth_headers

      expect(response).to have_http_status(200)
    end

    it 'should error when an invalid id is passed' do
      get singular_path(user, 0), headers: auth_headers

      expect(response).to have_http_status(404)
    end
  end

  describe '#invest' do
    let(:potential_investment) { create(:potential_investment, user: user) }
    let(:path) { "/api/v1/users/#{user.id}/potential_investments/#{potential_investment.id}/invest" }

    let(:property2) { create(:property_regular) }
    let!(:item_1) { create(:potential_investment_item, potential_investment: potential_investment, property: property, quantity: quantity) }
    let!(:item_2) { create(:potential_investment_item, potential_investment: potential_investment, property: property2, quantity: quantity) }

    context 'successful' do
      it 'successfully creates the order' do
        expect(potential_investment.items.size).to eq(2)

        orders_before = Share::BuyOrder.count

        put path, headers: auth_headers

        expect(response).to have_http_status(201)
        expect(Share::BuyOrder.count).to eq(orders_before + 2)
      end
    end

    context 'kyc pending' do
      let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }
      let(:request) { put path, headers: auth_headers }

      it 'queues for later' do
        expect { request }.to change { User::QueuedAction.count }.by(1)
        expect { request }.to change { Share::BuyOrder.count }.by(0)
      end
    end
  end

  def index_path(user)
    "/api/v1/users/#{user.id}/potential_investments"
  end

  def singular_path(user, id)
    "/api/v1/users/#{user.id}/potential_investments/#{id}"
  end
end
