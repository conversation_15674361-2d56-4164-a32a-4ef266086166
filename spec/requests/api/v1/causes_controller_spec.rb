require 'rails_helper'

RSpec.describe Api::V1::CausesController, type: :request do
  let(:path) { '/api/v1/causes' }
  let(:new_cause) { create(:property_cause, :with_photos) }

  after do
    expect(response).to have_http_status(200)
  end

  describe 'GET #index' do
    it 'returns all properties' do
      create_list(:property_cause, 2)

      get path

      expect(response_json.size).to eq(2)
    end

    it 'doesnt return hidden cause' do
      visible_cause = create(:property_cause, visible: true)
      hidden_cause = create(:property_cause, visible: false)

      get path

      expect(response_json.size).to eq(1)
      expect(response.body).to include(visible_cause.name)
      expect(response.body).not_to include(hidden_cause.name)
    end

    it 'returns nested photos' do
      create(:property_cause, :with_photos)

      get path

      expect(response_json.first['photos'].size).to eq(5)
    end
  end

  describe 'GET #show' do
    it 'returns a single cause' do
      get singular_path(new_cause.id)

      expect(response_json['photos'].size).to eq(5)
    end
  end

  describe 'GET #by_slug' do
    it 'returns a single cause' do
      get '/api/v1/causes/by_slug', params: { slug: new_cause.slug }

      expect(response).to have_http_status(200)
    end
  end

  def singular_path(id)
    "/api/v1/causes/#{id}"
  end
end
