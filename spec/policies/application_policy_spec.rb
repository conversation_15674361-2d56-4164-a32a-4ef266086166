require 'rails_helper'

RSpec.describe ApplicationPolicy do
  let(:user) { User.new }
  let(:record) { :bank_account }
  let(:policy) { described_class.new(user, record) }

  describe "#index?" do
    it "returns false" do
      expect(policy.index?).to be false
    end
  end

  describe "#show?" do
    it "returns false" do
      expect(policy.show?).to be false
    end
  end

  describe "#create?" do
    it "returns false" do
      expect(policy.create?).to be false
    end
  end

  describe "#new?" do
    it "returns the same as create?" do
      expect(policy.new?).to eq(policy.create?)
    end
  end

  describe "#update?" do
    it "returns false" do
      expect(policy.update?).to be false
    end
  end

  describe "#edit?" do
    it "returns the same as update?" do
      expect(policy.edit?).to eq(policy.update?)
    end
  end

  describe "#destroy?" do
    it "returns false" do
      expect(policy.destroy?).to be false
    end
  end

  describe "Scope" do
    let(:scope) { double("scope") }
    let(:policy_scope) { described_class::Scope.new(user, scope) }

    describe "#resolve" do
      it "returns the full scope" do
        expect { policy_scope.resolve }.to raise_error(NotImplementedError, "You must define #resolve in ApplicationPolicy::Scope")

      end
    end
  end
end