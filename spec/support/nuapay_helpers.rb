module NuapayHelpers
  def stub_nuapay_create_account_service(success: true, data: nil)
    default_data = {
      'data' => {
        'id' => '123',
        'name' => 'test',
        'status' => 'ACTIVE',
        'account_number' => '********',
        'branch_code' => '111111',
        'identification' => { 'iban' => 'GBBIC111111********555' }
      }
    }

    response = double(
      success?: success,
      body: data || default_data
    )

    allow(Nuapay::Accounts::CreateService).to receive(:new)
                                                .and_return(double(call: response))
  end
  def stub_nuapay_account_balance_service(success: true, data: nil,  balance: 1000)
    default_data = {
      'data' => [
        {
          'balance' => {
            'type' => 'AVAILABLE_BALANCE',
            'amount' => balance,
          }
        }
      ]
    }

    response = double(
      success?: success,
      body: data || default_data
    )
    allow(Nuapay::Accounts::BalanceService).to receive(:new)
                                                .and_return(double(call: response))
  end

  def stub_nuapay_process_dividends_service(success: true, data: nil)
    default_data = {
      'data' => [
        {
          'id' => '1234',
          'uri' => 'example.com/1234',
          'paymentAmount' => 100,
          'endToEndId' => 1122
        }
      ]
    }

    response = double(
      success?: success,
      body: data || default_data
    )
    allow(Nuapay::CreditTransfers::Batch::ProcessDividendsService).to receive(:new)
                                                 .and_return(double(call: response))
  end

  def stub_nuapay_batch_ct_creation(success: true, data: nil)
    default_data = {
      'data' =>
        {
          'id' => '1234',
          'uri' => 'example.com/1234',
          'totalAmount' => 100,
          'currency' => 'GBP',
          'batchStatus' => 'QUEUED'
        }

    }

    response = double(
      success?: success,
      body: data || default_data,
      error: ''
    )
    allow(Nuapay::CreditTransfers::Batch::CreateService).to receive(:new)
                                                              .and_return(double(call: response))
  end
end