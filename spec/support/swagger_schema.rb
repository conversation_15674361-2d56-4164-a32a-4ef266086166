module SwaggerSchema
  def self.bank_account_schema
    {
      id: { type: :integer },
      address_1: { type: :string },
      address_2: { type: :string },
      city: { type: :string },
      region: { type: :string },
      post_code: { type: :string },
      country: { type: :string },
      name: { type: :string },
      sort_code: { type: :string },
      account_number: { type: :string },
      active: { type: :boolean }
    }
  end

  def self.cause_schema
    {
      id: { type: :integer },
      available_shares: { type: :integer },
      certification_level_ids: { type: :array, items: { type: :integer } },
      created_at: { type: :string, format: :date },
      description: { type: :string },
      funded: { type: :boolean },
      name: { type: :string },
      placeholder: { type: :boolean },
      share_count: { type: :integer },
      share_price: { type: :string }, # JSON Decimal
      slug: { type: :string },
      target_amount: { type: :integer },
      type: { type: :string },
      unique_contributors: { type: :integer },
      documents: { type: :array, items: { type: :object, properties: {
        id: { type: :integer },
        attachment_url: { type: :string },
        title: { type: :string }
      } } },
      news_items: { type: :array, items: { type: :object, properties: {
        id: { type: :integer },
        content: { type: :string },
        date: { type: :string },
        title: { type: :string }
      } } },
      photos: { type: :array, items: { type: :object, properties: {
        attachment_url: { type: :string },
        medium_url: { type: :string },
        thumbnail_url: { type: :string }
      } } },
      tags: { type: :array, items: { type: :object, properties: {
        attachment_url: { type: :string },
        name: { type: :string },
        thumbnail_url: { type: :string }
      } } }
    }
  end

  def self.certification_level_schema
    {
      id: { type: :integer },
      attachment: { type: :string },
      brief: { type: :string },
      default: { type: :boolean },
      description: { type: :string },
      name: { type: :string },
      kind: { type: :string },
      position: { type: :integer },
      questions: { type: :array, items: {
        properties: {
          id: { type: :integer },
          question: { type: :string },
          answers: { type: :array, items: {
            properties: {
              id: { type: :integer },
              answer: { type: :string },
              correct: { type: :boolean }
            }
          } }
        }
      } }
    }
  end

  def self.dashboard_schema
    {
      annualised_return: { type: :string }, # JSON Decimal
      profit: { type: :integer },
      total_value: { type: :integer },
      investments: { type: :array, items: {
        properties: {
          total_amount: { type: :integer },
          total_growth: { type: :integer },
          total_income: { type: :integer },
          property: { type: :object, properties: SwaggerSchema.property_schema },
          share_orders: { type: :array, items:
            { type: :object, properties: SwaggerSchema.share_order_schema }
          },
          share_logs: { type: :array, items:
            { type: :object, properties: SwaggerSchema.share_log_schema }
          },
          dividends: { type: :array, items:
            { type: :object, properties: SwaggerSchema.dividend_schema }
          },
        }
      } }
    }
  end

  def self.dividend_schema
    {
      id: { type: :integer },
      amount: { type: :string }, # JSON Decimal
      created_at: { type: :string, format: :date },
      property_id: { type: :integer },
      updated_at: { type: :string, format: :date },
      property: { type: :object, properties: SwaggerSchema.property_schema }
    }
  end

  def self.investment_document_schema
    {
      name: { type: :string },
      description: { type: :string },
      attachment_url: { type: :string }
    }
  end

  def self.mandate_schema
    {
      id: { type: :integer },
      amount: { type: :integer },
      bank_account_id: { type: :string },
      day: { type: :integer },
      mangopay_mandate: { type: :object, properties: {
        id: { type: :string },
        bank_account_id: { type: :string },
        user_id: { type: :string },
        return_url: { type: :string },
        redirect_url: { type: :string },
        document_url: { type: :string },
        status: { type: :string }
      } }
    }
  end

  def self.potential_investment_schema
    {
      id: { type: :integer },
      total: { type: :integer },
      user_id: { type: :integer },
      items: { type: :array, items: {
        type: :object,
        properties: {
          id: { type: :integer },
          quantity: { type: :integer },
          property: { type: :object, property: SwaggerSchema.property_schema }
        }
      } }
    }
  end

  def self.payment_log_schema
    {
      id: { type: :integer },
      amount: { type: :integer },
      created_at: { type: :string, format: :date },
      direction: { type: :string },
      failed_at: { type: :string, format: :date, 'x-nullable': true },
      kind: { type: :string },
      status: { type: :string },
      successful_at: { type: :string, format: :date, 'x-nullable': true },
      dividend: { type: :object, 'x-nullable': true }
    }
  end

  def self.property_schema
    {
      id: { type: :integer },
      address_1: { type: :string },
      address_2: { type: :string },
      available_shares: { type: :integer },
      certification_level_ids: { type: :array, items: { type: :integer } },
      city: { type: :string },
      created_at: { type: :string, format: :date },
      description: { type: :string },
      easy_exit: { type: :boolean },
      funded: { type: :boolean },
      hpi: { type: :float },
      hpi_area: { type: :string },
      investment_case_and_risk: { type: :string },
      name: { type: :string },
      placeholder: { type: :boolean },
      postcode: { type: :string },
      property_amount: { type: :integer },
      property_fee_deferred_tax: { type: :integer },
      property_fee_legal_and_professional: { type: :integer },
      property_fee_pre_let_expenses: { type: :integer },
      property_fee_repairs_provision: { type: :integer },
      property_fee_stamp_duty: { type: :integer },
      rent_amount: { type: :integer },
      rental_fee_allowance_for_voids: { type: :integer },
      rental_fee_corporation_tax: { type: :integer },
      rental_fee_deferred_fees: { type: :integer },
      rental_fee_insurance: { type: :integer },
      rental_fee_maintenance_allowance: { type: :integer },
      rental_fee_management: { type: :integer },
      rental_fee_spv_charge: { type: :integer },
      rental_yield: { type: :string },
      share_count: { type: :integer },
      share_price: { type: :string },
      slug: { type: :string },
      spv_name: { type: :string },
      target_amount: { type: :integer },
      type: { type: :string },
      documents: { type: :array, items: { type: :object, properties: {
        id: { type: :integer },
        attachment_url: { type: :string },
        title: { type: :string }
      } } },
      floorplans: { type: :array, items: { type: :object, properties: {
        attachment_url: { type: :string },
        medium_url: { type: :string },
        thumbnail_url: { type: :string }
      } } },
      legal_documents: { type: :array, items: { type: :object, properties: {
        id: { type: :integer },
        attachment_url: { type: :string },
        title: { type: :string }
      } } },
      news_items: { type: :array, items: { type: :object, properties: {
        id: { type: :integer },
        content: { type: :string },
        date: { type: :string },
        title: { type: :string }
      } } },
      photos: { type: :array, items: { type: :object, properties: {
        attachment_url: { type: :string },
        medium_url: { type: :string },
        thumbnail_url: { type: :string }
      } } },
      tags: { type: :array, items: { type: :object, properties: {
        attachment_url: { type: :string },
        name: { type: :string },
        thumbnail_url: { type: :string }
      } } }
    }
  end

  def self.share_log_schema
    {
      id: { type: :integer },
      created_at: { type: :string, format: :date },
      property_id: { type: :integer },
      quantity: { type: :integer },
      total_amount: { type: :integer },
      updated_at: { type: :string, format: :date },
      property: { type: :object, properties: SwaggerSchema.property_schema }
    }
  end

  def self.share_order_schema
    {
      id: { type: :integer, 'x-nullable': true },
      aasm_state: { type: :string },
      created_at: { type: :string, format: :date, 'x-nullable': true },
      type: { type: :string },
      property_id: { type: :integer, 'x-nullable': true },
      quantity: { type: :integer, 'x-nullable': true },
      quantity_remaining: { type: :integer },
      quantity_available_to_sell: { type: :integer },
      reason: { type: :string, 'x-nullable': true },
      total_amount: { type: :integer, 'x-nullable': true },
      updated_at: { type: :string, format: :date, 'x-nullable': true },
      property: { type: :object, properties: SwaggerSchema.property_schema, 'x-nullable': true },
      user: { type: :object, properties: SwaggerSchema.user_schema }
    }
  end

  def self.queued_action_schema
    {
      id: { type: :integer },
      aasm_state: { type: :string },
      amount: { type: :integer, 'x-nullable': true },
      type: { type: :string },
      target: {
        type: :object,
        properties: SwaggerSchema.potential_investment_schema,
        'x-nullable': true
      }
    }
  end

  def self.tax_statement_schema
    {
      investment_costs: { type: :integer },
      sales_proceeds: { type: :integer },
      total_dividends: { type: :integer },
      total_fees: { type: :integer },
      subtotal: { type: :integer },
      items: {
        type: :array,
        items: {
          type: :object,
          properties: {
            investment_costs: { type: :integer },
            sales_proceeds: { type: :integer },
            total_dividends: { type: :integer },
            total_fees: { type: :integer },
            subtotal: { type: :integer },
            property: { type: :object, properties: SwaggerSchema.property_schema }
          }
        }
      }
    }
  end

  def self.user_schema
    {
      id: { type: :integer },
      aasm_state: { type: :string },
      authentication_token: { type: :string, 'x-nullable': true },
      business_name: { type: :string, 'x-nullable': true },
      business_number: { type: :string, 'x-nullable': true },
      call_me: { type: :boolean },
      confirmed_at: { type: :string, format: :date, 'x-nullable': true },
      country_of_residence: { type: :string, 'x-nullable': true },
      created_at: { type: :string, format: :date },
      date_of_birth: { type: :string, 'x-nullable': true },
      email: { type: :string },
      employment_status: { type: :string, 'x-nullable': true },
      experience: { type: :string, 'x-nullable': true },
      first_name: { type: :string, 'x-nullable': true },
      income_range: { type: :integer, 'x-nullable': true },
      last_name: { type: :string, 'x-nullable': true },
      legal_type: { type: :string, 'x-nullable': true },
      marketing: { type: :boolean, 'x-nullable': true },
      middle_name: { type: :string, 'x-nullable': true },
      nationality: { type: :string, 'x-nullable': true },
      occupation: { type: :string, 'x-nullable': true },
      phone_number: { type: :string, 'x-nullable': true },
      planned_investment: { type: :string, 'x-nullable': true },
      previous_state: { type: :string, 'x-nullable': true },
      title: { type: :string, 'x-nullable': true },
      type: { type: :string },
      updated_at: { type: :string, format: :date }
    }
  end

  def self.wallet_schema
    {
      id: { type: :string },
      description: { type: :string },
      balance: { type: :integer },
      currency: { type: :string },
      owner_id: { type: :string }
    }
  end

  # Errors

  def self.error_schema
    {
      errors: {
        type: :object,
        properties: {
          base: {
            type: :array,
            items: { type: :string }
          }
        }
      }
    }
  end
end
