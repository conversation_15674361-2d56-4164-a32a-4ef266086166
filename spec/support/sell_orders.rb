require 'rails_helper'

shared_examples 'it acts like a sell order' do
  it 'should accept when the user has enough shares' do
    buy_shares

    orders_before = Share::Order.count

    post singular_path(user), params: { property_id: property.id,
                                                       quantity: quantity,
                                                       kind: kind },
                                             headers: auth_headers_for_user(user)

    expect(response).to have_http_status(201)
    expect(Share::Order.count).to eq(orders_before + 1)
  end

  it 'should reject if the property is unfunded / not easy exit' do
    buy_shares

    post singular_path(user), params: { property_id: invalid_property.id,
                                                       quantity: quantity,
                                                       kind: kind },
                                             headers: auth_headers_for_user(user)


    expect(response).to have_http_status(422)
    expect(response_json['errors']['base'].size).to be > 0
    expect(response_json['errors']['base']).to include("You can't sell shares in this property")
  end

  it 'should reject if the user doesnt have enough shares' do
    destroy_shares

    post singular_path(user), params: { property_id: invalid_property.id,
                                                       quantity: quantity,
                                                       kind: kind },
                                             headers: auth_headers_for_user(user)

    expect(response).to have_http_status(422)
    expect(response_json['errors']['base'].size).to be > 0
    expect(response_json['errors']['base']).to include("You don't have enough shares to sell that many")
  end
end
