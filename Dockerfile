# TAG Link https://hub.docker.com/r/phusion/passenger-ruby33/tags
FROM phusion/passenger-ruby33:3.1.0
# How many bundler jobs to run in parallel - set with --build-arg="BUNDLER_JOBS=2"
ARG BUNDLER_JOBS=1

# Note: This Dockerfile is roughly in reverse order of likelihood to change for performance (so cached layers
#       at the bottom of the stack can be re-used rather than rebuilt)

# Set correct environment variables.
ENV HOME /root

# Use baseimage-docker's init process.
CMD ["/sbin/my_init"]

# Install public keys for hosts
RUN mkdir -p -m 0600 /home/<USER>/.ssh \
  && ssh-keyscan bitbucket.org >> /home/<USER>/.ssh/known_hosts

# Update Bundler
RUN gem install bundler -v=2.5.22

# Add your apt-get packages as part of this step
RUN apt-get update -qq \
  && DEBIAN_FRONTEND=noninteractive apt-get install -yq --no-install-recommends \
  imagemagick shared-mime-info \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /home/<USER>/webapp/tmp \
  && truncate -s 0 /var/log/*log

# Start Nginx / Passenger and remove the default site
RUN rm -f /etc/service/nginx/down \
  && rm /etc/nginx/sites-enabled/default

# COPY the nginx site and config
COPY docker/nginx.conf /etc/nginx/sites-enabled/webapp.conf
COPY docker/rails-env.conf /etc/nginx/main.d/rails-env.conf

# Switch to app user - which runs the application process - to bundle install
USER app:app
# Set HOME env var used by bundler to store bundle and config files
ENV HOME /home/<USER>

# Copy Gemfiles for bundler to use to install bundle
# Note: We copy the Gemfile over and install the bundle ahead of the app source files because they're less
#       likely to change, so we can use them as cached layers for subsequent builds
COPY --chown=app:app Gemfile Gemfile.lock /tmp/
WORKDIR /tmp

# Mount the ssh-agent from the host for the app user (uid: 9999) and install the bundle
RUN --mount=type=ssh,uid=9999 bundle config --global path /home/<USER>/.bundle \
  && bundle install --jobs $BUNDLER_JOBS

# COPY the Rails app - See .dockerignore file for what is excluded from being copied into the image
COPY --chown=app:app . /home/<USER>/webapp/

WORKDIR /home/<USER>/webapp

# Build API Documentation
RUN RAILS_ENV=swagger bundle exec rake rswag:specs:swaggerize

# Compile assets and remove temp files
RUN bundle exec rake assets:precompile \
  && rm -rf ./tmp/*

# Switch back to root user, because base image requires it to correctly run
# See: https://github.com/phusion/passenger-docker/issues/250
USER root
