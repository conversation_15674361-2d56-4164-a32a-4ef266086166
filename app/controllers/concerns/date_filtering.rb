module DateFiltering
  extend ActiveSupport::Concern

  included do
    before_action :parse_dates
  end

  private

  def date_params
    params.permit(:start_date, :end_date)
  end

  def parse_dates
    @start_date = Date.parse(date_params[:start_date]) rescue nil
    @end_date   = Date.parse(date_params[:end_date]) rescue nil

    head :unprocessable_entity if @start_date && @end_date && @start_date > @end_date
  end
end
