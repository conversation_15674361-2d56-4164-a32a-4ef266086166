# == UOWN API
# ==== Overview
# The UOWN API is a RESTful JSON API built with ruby on rails.
#
# For more information on REST please see:
# https://en.wikipedia.org/wiki/Representational_state_transfer
#
# ==== Authentication
# Any request that can modify or access private data must accompanied
# by an authentication token.
#
# An authentication token is generated when creating a new user session
# (/users/sessions.json POST) or new user (/users/registrations.json POST).
# The response packet includes a user object that contains and 'id' parameter
# and a 'authentication_token' parameter. An example packet is shown below:
#
#   {
#     "id":17,
#     "aasm_state":"kyc_regular_is_pending",
#     "address_1":"123 Somewhere Street",
#     "address_2":null,
#     "authentication_token":"yV6cZJZEDydqisZEjhPx",
#     "call_me":true,
#     "certification_level_id":3,
#     "city":"Leeds",
#     "confirmed_at":"2017-12-04T14:49:32.000Z",
#     "country":"GB",
#     "country_of_residence":"GB",
#     "created_at":"2016-09-14T15:44:48.000Z",
#     "date_of_birth":"1985-01-01",
#     "email":"<EMAIL>",
#     "first_name":"John",
#     "income_range":6,
#     "last_name":"Smith",
#     "middle_name":null,
#     "nationality":"GB",
#     "occupation":"Entrepreneur",
#     "phone_number":"0113 000 0000",
#     "post_code":"LS12 1DR",
#     "region":null,
#     "reset_token":null,
#     "title":"Mr",
#     "type":"User::Natural",
#     "updated_at":"2017-12-20T14:17:17.378Z"
#  }
#
# These parameters need to then be sent with any further requests as follows
#
# * user_id - The 'id' parameter in the returned user object (this usually forms part of the url)
# * authentication_token - The 'authentication_token' parameter in the returned user object
#
# Failure to send the correct authorization keys will result in a 403 error response
#
require 'posthog/event_names'

class Api::V1::BaseController < ApplicationController
  include ActionController::HttpAuthentication::Token::ControllerMethods
  include Pundit::Authorization

  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized
  rescue_from ActiveRecord::RecordNotFound, with: :not_found

  private

  def load_and_authenticate_user
    if params[:user_id].present? && request.headers['Authorization'].present?
      authenticate_with_http_token do |token|
        @user = User.authenticate_from_token(params[:user_id], token)
      end
    end

    head :unauthorized unless @user
  end

  def check_user_in_kyc_state
    head :not_found unless @user.kyc_light_is_complete? ||  @user.kyc_regular_is_pending? || @user.kyc_regular_is_complete?
  end

  def not_found
    head :not_found
  end

  def capture_posthog(distinct_id:, event_name:, event_properties: {})
    return if distinct_id.blank? || event_name.blank?

    Posthog.client.capture(
      {
        distinct_id: distinct_id,
        event: posthog_event(event_name),
        properties: event_properties
      }
    )
  rescue => e
    Airbrake.notify(e)
  end

  def posthog_event(event_name)
    posthog_event_name = Posthog::EVENT_NAMES[event_name] || event_name.to_s
    case Rails.env
    when 'development', 'staging'
      "DEV-#{posthog_event_name}"
    when 'production'
      posthog_event_name
    else
      "#{Rails.env}-#{posthog_event_name}"
    end
  end

  def user_not_authorized
    msg = 'You are not authorized to perform this action.'
    error(msg)
  end
  
  def error(msg='Something went wrong, try again.', status: :unprocessable_entity)
    render json: { errors: { base: [msg] } }, status: status
  end

  def success(data)
    render json: data, status: :ok
  end

  def json_parse(data)
    JSON.parse(data)
  end

  def to_penny(amt)
    (amt.to_d * 100).to_i
  end
end
