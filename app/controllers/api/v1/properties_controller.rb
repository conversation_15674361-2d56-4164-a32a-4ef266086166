class Api::V1::PropertiesController < Api::V1::BaseController
  def index
    @properties = Property.visible
                          .where.not(type: 'Property::Cause')
                          .includes(:documents, :floorplans, :legal_documents, :news_items, :photos, :tags)

    render json: @properties
  end

  def show
    @property = Property.includes(:documents, :floorplans, :news_items, :photos, :tags)
                        .find(params[:id])

    render json: @property
  end

  def by_slug
    @property = Property.includes(:documents, :floorplans, :news_items, :photos, :tags)
                        .find_by!(slug: params[:slug])

    render json: @property
  end
end
