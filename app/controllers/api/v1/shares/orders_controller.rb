class Api::V1::Shares::OrdersController < Api::V1::BaseController
  before_action :load_and_authenticate_user

  def index
    @share_orders = @user.share_orders
                         .includes(property: [:documents, :floorplans, :photos, :tags])
                         .order(created_at: :desc)

    render json: @share_orders, include: '**'
  end

  def new
    @share_order = object_class.new(allowed_params.merge(user_id: @user.id))

    render json: @share_order, include: '**'
  end

  def create
    @share_order = object_class.new(allowed_params.merge(user_id: @user.id))

    if @share_order.save
      render json: @share_order, status: :created
    else
      render json: @share_order, status: :unprocessable_entity
    end
  end

  def show
    @share_order = @user.share_orders
                        .includes(property: [:documents, :floorplans, :photos, :tags])
                        .find(params[:id])

    render json: @share_order, include: '**'
  end

  def cancel
    @share_order = Share::Order.where(aasm_state: ['active', 'pending'])
                               .find_by(id: params[:id], user: @user)

    if @share_order
      @share_order.queue_cancellation!
      head :ok
    else
      render json: { errors: { base: ['Order has already been cancelled'] } }, status: :unprocessable_entity
    end
  end

  private

  def allowed_params
    params.permit(:property_id, :quantity)
  end

  def object_class(kind = params[:kind])
    case kind
    when 'buy'
      Share::BuyOrder
    when 'sell'
      Share::SellOrder
    when 'easy_exit'
      Share::EasyExitOrder
    else
      Share::BuyOrder
    end
  end
end
