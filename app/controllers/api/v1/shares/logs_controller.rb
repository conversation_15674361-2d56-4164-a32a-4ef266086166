class Api::V1::Shares::LogsController < Api::V1::BaseController
  before_action :load_and_authenticate_user

  include DateFiltering

  def index
    @share_logs = @user.share_logs
                       .includes(:buy_order, :sell_order, property: [:documents, :floorplans, :photos, :tags])
                       .order(created_at: :desc)

    @share_logs = @share_logs.where(created_at: @start_date..@end_date) if @start_date && @end_date

    render json: @share_logs, include: '**'
  end
end
