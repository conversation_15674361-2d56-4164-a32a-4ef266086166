class Api::V1::CausesController < Api::V1::BaseController
  def index
    @causes = Property::Cause.visible.includes(:news_items, :photos)

    render json: @causes
  end

  def show
    @cause = Property::Cause.includes(:news_items, :photos).find(params[:id])

    render json: @cause
  end

  def by_slug
    @cause = Property::Cause.includes(:news_items, :photos).find_by!(slug: params[:slug])

    render json: @cause
  end
end
