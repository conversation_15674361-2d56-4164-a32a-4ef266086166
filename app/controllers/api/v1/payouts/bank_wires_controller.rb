class Api::V1::Payouts::BankWiresController < Api::V1::BaseController
  before_action :load_and_authenticate_user
  before_action :check_user_in_kyc_regular_or_pending

  def create
    model = @user.kyc_regular_is_pending? ? User::QueuedActionPayout : Mango::Payout
    alert_to_admin(model)

    payout = model.create(amount: allowed_params[:amount],
                          bank_account_id: allowed_params[:bank_account_id],
                          user: @user)
    render json: payout
  rescue => e
    ph_properties = { amount: ( allowed_params[:amount].to_i / 100 ), bank_account_id: allowed_params[:bank_account_id], error: e.message }
    capture_posthog(distinct_id: @user.id, event_name: :withdrawal_failed, event_properties: ph_properties)
    Airbrake.notify(e, allowed_params)

    render json: { errors: { base: [e.message] } }, status: :unprocessable_entity
  end

  private

  def allowed_params
    params.permit(:amount, :bank_account_id)
  end

  def check_user_in_kyc_regular_or_pending
    head :not_found unless @user.kyc_regular_is_complete? || @user.kyc_regular_is_pending?
  end

  def alert_to_admin(model)
    return  unless model.first_payout_bank?(user: @user, bank_account_id: allowed_params[:bank_account_id])

    BankAccountNotifier.with(record: @user, msg: "First payout to a new bank account.").deliver
  end
end
