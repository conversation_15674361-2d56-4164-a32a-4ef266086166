class Api::V1::Payins::GooglePaysController < Api::V1::BaseController
  before_action :load_and_authenticate_user

  def create
    res = Mango::GooglePayService.new(@user.mangopay_id, @user.wallet_id, to_penny(allowed_params[:amount]),
                                      allowed_params[:token], allowed_browser_signature,
                                      allowed_params[:potential_investment_id], to_penny(allowed_params[:fee])).call
    if res.success?
      transaction = json_parse(res.data.body)
      transaction = MangoPay::PayIn.fetch(transaction['Id'])
      status = transaction['Status'] == 'SUCCEEDED' ? 'successful!' : 'failure!'
      log = PaymentLog.create_from_mangopay(user: @user, mangopay_object: transaction,
                                      amount: transaction['CreditedFunds']['Amount'],
                                      potential_investment_id: allowed_params[:potential_investment_id])
      log.public_send(status, transaction['Status']) unless transaction['Status'] == 'CREATED'
      PayinsCardDelivery.notify(:successful, @user, transaction) if transaction['Status'] == 'SUCCEEDED' && allowed_params[:potential_investment_id].blank?
      success(transaction)
    else
      error_msg = json_parse(res.error)['Errors'].map { |attr, value| "#{attr} : #{value}" }.to_sentence
      Airbrake.notify(error_msg)
      error(error_msg)
    end
  rescue StandardError => e
    Airbrake.notify(e)
    error(e.message)
  end

  def handle_response
    resp = MangoPay::PayIn.fetch(allowed_params[:transactionId])
    status = resp['Status'] == 'SUCCEEDED' ? 'successful!' : 'failure!'
    log = @user.payment_logs.where(potential_investment_id: params[:p_inv_id].presence).last
    log.public_send(status, resp['Status']) if log.present?

    raise "Transaction #{resp['Status']}: #{resp['ResultMessage']}" unless resp['Status'] == 'SUCCEEDED'
    PayinsCardDelivery.notify(:successful, @user, resp) if params[:p_inv_id].blank?
    success(resp)
  rescue StandardError => e
    error(e.message)
  end

  private

  def allowed_params
    params.permit(:transactionId, :status, :amount, :token, :fee, :potential_investment_id, :browser_infos)
  end

  def allowed_browser_signature
    params.require(:browser_infos).permit(:JavaEnabled, :Language, :ColorDepth, :ScreenHeight, :ScreenWidth,
                                          :TimeZoneOffset, :JavascriptEnabled, :AcceptHeader, :IpAddress, :UserAgent)
  end
end
