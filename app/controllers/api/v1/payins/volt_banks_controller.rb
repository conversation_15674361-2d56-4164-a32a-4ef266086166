class Api::V1::Payins::VoltBanksController < Api::V1::BaseController
  before_action :load_and_authenticate_user, except: [:webhook_payment, :webhook_account_creation, :webhook_batch_credit_transfer, :index]

  def index
    response = ::Volt::Banks::ListService.new.call
    render json: response.body
  end

  def create
    params = Volt::RequestBodyService.new(req_body: 'create_payment', payload: allowed_params.merge!(user: @user)).call
    res = ::Volt::Payments::CreateService.new(params: params).call
    if res.success?
      PaymentLog.create_from_remote(user: @user,
                                    remote_object: res.body,
                                    amount: allowed_params[:amount],
                                    potential_investment_id: allowed_params[:p_inv_id])
      url = res.body&.dig('paymentInitiationFlow','details','redirect','url')
      raise 'Redirect URL no found.' if url.blank?
      render json: {url: url}
    else
      Airbrake.notify(res.error)
      render json: { base: [res.error] }, status: :unprocessable_entity
    end
  rescue StandardError => e
    Airbrake.notify(e.message)
    render json: { base: [e.message] }, status: :unprocessable_entity
  end

  def show
    res = ::Volt::Payments::ShowService.new(payment_id: allowed_params[:id]).call
    if res.success?
      render json: res.body
    else
      render json: { base: [res.error] }, status: :unprocessable_entity
    end
  end

  def handle_response
    log = PaymentLog.find_by(potential_investment_id: params[:p_inv_id])
    log.failure!('FAILED') if params[:status] == 'failed'
    render json: {}, status: :ok
  end

  def webhook_payment
    20.times { Rails.logger.info "👉👉👉 Payment complete webhook called. #{params} 👈👈👈" }
    if params[:payment] && ::Volt::Webhooks::SignatureValidationService.new(params: params).call
      PotentialInvestment.process_payment_webhook(params[:payment])
      render json: {}, status: :ok
    else
      20.times { Rails.logger.info "👉👉👉 Payment complete webhook signature failed. #{params} 👈👈👈" }
      Airbrake.notify("Invalid Payment webhook signature #{params}")
      render json: {}, status: :unprocessable_entity
    end
  end

  def webhook_account_creation
    20.times { Rails.logger.info "👉👉👉 account creation webhook called. #{params} 👈👈👈" }
    if validate_webhook_signature(params)
      RemoteBankAccount.process_account_webhook(params[:resourceId])
      render json: {}, status: :ok
    else
      Airbrake.notify("Invalid Account setup webhook signature #{params}")
      render json: {}, status: :unprocessable_entity
    end
  end

  def webhook_batch_credit_transfer
    20.times { Rails.logger.info "👉👉👉 Webhook batch credit transfer called. #{params} 👈👈👈" }
    if validate_webhook_signature(params)
      Property::Dividend.process_batch_ct_webhook(params[:resourceId])
      render json: {}, status: :ok
    else
      Airbrake.notify("Invalid BCT webhook signature #{params}")
      render json: {}, status: :unprocessable_entity
    end
  end

  private

  def allowed_params
    params.permit(:amount, :id, :p_inv_id, :merchant_post_auth_url)
  end
  def generate_reference
    timestamp = Time.now.utc.strftime("%y%m%d%H%M") # 10 chars
    random_part = SecureRandom.alphanumeric(8).upcase # 8 chars
    "#{timestamp}#{random_part}"
  end

  def creditor_account(p_inv_id)
    account = PotentialInvestment.find(p_inv_id).properties.last.remote_bank_account
    raise 'Invalid Project account. Please contact to uown support.' if account.blank? || account.invalid_bank?

    iban = account.iban
    sort_code = iban[8, 6]           # Characters 9–14
    account_number = iban[14, 8]     # Characters 15–22
    {
      "identification": "#{sort_code}#{account_number}",
      "schemeName": "SortCodeAccountNumber"
    }
  end

  def debitor_account
    bank = @user.remote_bank_account
    return if bank.blank? || bank.invalid_bank?

    {
      "identification": bank.sort_code_account_number,
      "schemeName": "SortCodeAccountNumber"
    }
  end
end
