class Api::V1::Payins::CardsController < Api::V1::BaseController
  before_action :load_and_authenticate_user
  before_action :check_user_in_kyc_state

  def create
    begin
      card_registration = Mango::CardRegistration.create(user: @user)

      render json: card_registration, status: :created
    rescue MangoPay::ResponseError => ex
      capture_posthog(distinct_id: @user.id, event_name: :deposit_failed)
      failure_notification(ex.message)
      Airbrake.notify(ex)
      render json: { errors: { base: ['card registration failed'] } }, status: :unprocessable_entity
    rescue => e
      failure_notification(e.message)
      capture_posthog(distinct_id: @user.id, event_name: :deposit_failed)
      Airbrake.notify(e)
    end
  end

  def update
    ph_properties = { amount: allowed_params[:amount], payment_method: 'card' }
    url = "#{Rails.application.secrets.frontend_url}/payins/cards/confirm?potential_investment_id=#{allowed_params[:potential_investment_id]}"

    payin = Mango::Card.payin_direct(user: @user,
                                     card_id: params[:id],
                                     amount: allowed_params[:amount],
                                     return_url: url,
                                     potential_investment_id: allowed_params[:potential_investment_id],
                                     browser_info: init_browser_info)
    send_receipt(@user, payin) unless allowed_params[:potential_investment_id].present?

    capture_posthog(distinct_id: @user.id, event_name: :desposit_created, event_properties: ph_properties)

    render json: payin
  rescue MangoPay::Secure3DError => e
    ph_properties[:error] = e.message
    capture_posthog(distinct_id: @user.id, event_name: :deposit_failed, event_properties: ph_properties)
    failure_notification(e.message)
    Airbrake.notify(e)
    render json: e.message, status: :temporary_redirect
  rescue => e
    ph_properties[:error] = e.message
    capture_posthog(distinct_id: @user.id, event_name: :deposit_failed, event_properties: ph_properties)
    failure_notification(e.message)
    Airbrake.notify(e)
    render json: { errors: { base: [e.message] } }, status: :unprocessable_entity
  end

  def confirm
    payin = Mango::Card.confirm(user: @user,
                                transaction_id: params[:transaction_id],
                                potential_investment_id: params[:potential_investment_id])

    send_receipt(@user, payin) unless params[:potential_investment_id].present?

    render json: payin
  rescue => e
    failure_notification(e.message)
    Airbrake.notify(e)
    render json: { errors: { base: [e.message] } }, status: :unprocessable_entity
  end

  private

  def send_receipt(user, payin)
    PayinsCardDelivery.notify(:successful, user, payin)
  end

  def failure_notification(msg)
    PayinsCardDelivery.notify(:failed, @user, msg)
  end

  def allowed_params
    params.permit(:card_id, :amount, :potential_investment_id, browser_info: browser_info_params)
  end

  def browser_info_params
    %i[accept_header
       color_depth
       ip_address
       java_enabled
       javascript_enabled
       language
       screen_height
       screen_width
       time_zone_offset
       user_agent]
  end

  def init_browser_info
    @init_browser_info ||= Mango::BrowserInfo.new(allowed_params[:browser_info] || []) do |browser_info|
      browser_info.accept_header ||= request.headers['Accept']
      browser_info.ip_address ||= request.remote_ip
      browser_info.user_agent ||= request.user_agent
    end
  end
end
