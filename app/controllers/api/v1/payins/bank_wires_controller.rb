class Api::V1::Payins::BankWiresController < Api::V1::BaseController
  before_action :load_and_authenticate_user
  before_action :check_user_in_kyc_state

  def create
    ph_properties = { amount: ( allowed_params[:amount].to_d / 100 ), payment_method: 'Bank Wire' }
    bank_wire = Mango::BankWire.create(user: @user,
                                       amount: allowed_params[:amount],
                                       potential_investment_id: allowed_params[:potential_investment_id])
    admin_alert
    notify_user(@user, bank_wire)

    capture_posthog(distinct_id: @user.id, event_name: :deposit_completed, event_properties: ph_properties)

    render json: bank_wire
  rescue MangoPay::ResponseError => ex
    ph_properties[:error] = ex.message
    capture_posthog(distinct_id: @user.id, event_name: :deposit_failed, event_properties: ph_properties)

    render json: { errors: { base: ['Failed to transfer funds, contact to uown support center'] } }, status: :unprocessable_entity
  rescue => e
    ph_properties[:error] = e.message
    capture_posthog(distinct_id: @user.id, event_name: :deposit_failed, event_properties: ph_properties)

    render json: { errors: { base: [e.message] } }, status: :unprocessable_entity
  end

  private

  def allowed_params
    params.permit(:amount, :potential_investment_id)
  end

  def notify_user(user, bank_wire)
    payment_log = user.payment_logs.find_by(mangopay_id: bank_wire.id)

    PayinsBankWireDelivery.notify(:notify, payment_log)
  end

  def admin_alert
   return unless ::Users::Payins::Threshold.new(user: @user).call

   PayinThresholdNotifier.with(record: @user, msg: 'Payin Threshold alert').deliver
  end
end
