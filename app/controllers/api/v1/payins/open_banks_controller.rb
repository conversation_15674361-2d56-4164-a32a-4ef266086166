class Api::V1::Payins::OpenBanksController < Api::V1::BaseController
  before_action :load_and_authenticate_user, except: [:payment_complete, :webhook_account_creation, :webhook_batch_credit_transfer]

  def index
    response = ::Nuapay::Banks::ListService.new(params: { supportedcurrencies: 'GBP' }).call
    render json: response.body['data']
  end

  def create
    params = create_payment_params(allowed_params[:amount], allowed_params[:id], allowed_params[:p_inv_id])
    res = ::Nuapay::Payments::CreateService.new(params: params).call
    if res.success?

      PaymentLog.create_from_remote(user: @user,
                                    remote_object: res.body['data'],
                                    amount: (allowed_params[:amount].to_d * 100).to_i,
                                    potential_investment_id: allowed_params[:p_inv_id])
      render json: res.body['data']
    else
      Airbrake.notify(res.error)
      render json: { base: [res.error] }, status: :unprocessable_entity
    end
  rescue StandardError => e
    Airbrake.notify(e.message)
    render json: { base: [e.message] }, status: :unprocessable_entity
  end

  def show
    res = ::Nuapay::Payments::ShowService.new(payment_id: allowed_params[:id]).call
    if res.success?
      render json: res.body
    else
      render json: { base: [res.error] }, status: :unprocessable_entity
    end
  end

  def handle_response
    log = PaymentLog.find_by(potential_investment_id: params[:p_inv_id])
    log.failure!('FAILED') if params[:status] == 'failed'
    render json: {}, status: :ok
  end

  def payment_complete
    20.times { Rails.logger.info "👉👉👉 Payment complete webhook called. #{params} 👈👈👈" }
    ::Nuapay::Webhooks::PaymentCompleteService.new(remote_id: params[:resourceId]).call
    render json: {}, status: :ok
  end

  def webhook_account_creation
    account = Nuapay::Accounts::ShowService.new(account_id: params[:resourceId]).call
    if account.success?
      resp = account.body['data']
      status = resp['status']
      id = resp['id']

      local_account = RemoteBankAccount.find_by(remote_id: id)
      local_account.update_columns(owner_name: resp['name'], status: status, iban: resp['identification']['iban']) if local_account.present?
    else
      Airbrake.notify(account.error)
      render json: {}, status: :unprocessable_entity
    end
  rescue Exception => e
    Airbrake.notify(e.message)
    render json: {}, status: :unprocessable_entity
  end

  def webhook_batch_credit_transfer
    20.times { Rails.logger.info "👉👉👉 Webhook batch credit transfer called. #{params} 👈👈👈" }
    ::Nuapay::Webhooks::BatchCreditTransferService.new(remote_id: params[:resourceId]).call
    render json: {}, status: :ok
  end

  private

  def allowed_params
    params.permit(:amount, :id, :p_inv_id)
  end

  def create_payment_params(amount, bank_id, p_inv_id)
    req = {
      "amount": amount,
      "countryCode": "GB",
      "currency": "GBP",
      "language": "en",
      "integrationType": "SELF_HOSTED",
      "merchantPostAuthUrl": "#{Rails.application.secrets.frontend_url}/payins/open_bankings/handle_response?p_inv_id=#{p_inv_id}&user_id=#{@user.id}",
      "bankId": bank_id,
      "remittanceInformation": {
        "reference": generate_reference
      },
      "creditorAccount": creditor_account(p_inv_id),
      "paymentScheme": "UK_FASTER_PAYMENT"
    }
    req['debtorAccount'] = debitor_account if debitor_account.present?
    req
  end

  def generate_reference
    timestamp = Time.now.utc.strftime("%y%m%d%H%M") # 10 chars
    random_part = SecureRandom.alphanumeric(8).upcase # 8 chars
    "#{timestamp}#{random_part}"
  end

  def creditor_account(p_inv_id)
    account = PotentialInvestment.find(p_inv_id).properties.last.remote_bank_account
    raise 'Invalid Project account. Please contact to uown support.' if account.blank? || account.invalid_bank?

    iban = account.iban
    sort_code = iban[8, 6]           # Characters 9–14
    account_number = iban[14, 8]     # Characters 15–22
    {
      "identification": "#{sort_code}#{account_number}",
      "schemeName": "SortCodeAccountNumber"
    }
  end

  def debitor_account
    bank = @user.remote_bank_account
    return if bank.blank? || bank.invalid_bank?

    {
      "identification": bank.sort_code_account_number,
      "schemeName": "SortCodeAccountNumber"
    }
  end
end
