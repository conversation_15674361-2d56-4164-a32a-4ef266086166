# frozen_string_literal: true

module Api
  module V1
    module Users
      class MfaVerificationsController < Api::V1::BaseController
        before_action :load_and_authenticate_user
        def create
          entity = ::Twilio::Entity::Create.new(user_id: @user.id).call
          raise entity[:error] unless entity[:success]

          response = ::Twilio::Totp::Create.new(user: @user, twilio_entity: entity[:data]).call
          response[:success] ? success(response[:data].binding.to_json) : error(response[:message])
        rescue Exception => error
          error(error.message)
        end

        def verify_otp
          factor_id = ::Twilio::Entity::Show.new(user_id: @user.id)
                                          .call[:data]
                                          .factors.list.last.sid

          response = ::Twilio::Totp::Verify.new(user_id: @user.id, factor_id: factor_id, otp: params[:otp]).call
          
          return error(response[:error]) unless response[:success]

          return error('invalid otp') unless response[:data].status == 'verified'

          return success('verified'.to_json) if @user.update(factor_id: factor_id)

          error('some thing wrong')
        rescue Exception => e
          error(e.message)
        end

        def cancel
          response = ::Twilio::Totp::Challenge.new(user_id: @user.id, factor_id: @user.factor_id, otp: params[:otp]).call
          return error(response[:error]) unless response[:success]
          return error('Invalid OTP') if response[:data].status != 'approved'

          entity_deleted = ::Twilio::Entity::Delete.new(user_id: @user.id).call

          return error(entity_deleted[:error]) unless entity_deleted[:success]

          @user.update(factor_id: nil)
          success('MFA has been removed'.to_json)
        rescue Exception => e
          error(e.message)
        end

        def challenge
          response = ::Twilio::Totp::Challenge.new(user_id: @user.id, factor_id: @user.factor_id, otp: params[:otp]).call
          return error(response[:error]) unless response[:success]
          return error('Invalid OTP') if response[:data].status != 'approved'
          success('verified'.to_json)
        rescue Exception => e
          error(e.message)
        end
      end
    end
  end
end
