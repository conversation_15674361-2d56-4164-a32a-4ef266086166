class Api::V1::Users::PasswordsController < Api::V1::BaseController
  # == [POST] /api/v1/users/passwords.json
  # Request a password reset email
  # ==== Required
  # * email - the users email address
  # ==== Returns
  # * 201 - success
  # * 422 - failure - no user with that email
  def create
    @user = User.find_by(email: allowed_params[:email])

    if @user
      @user.ensure_reset_token!
      UserPasswordDelivery.notify(:reset, @user)
      capture_posthog(distinct_id: @user.id, event_name: :new_password_requested)
      head :created
    else
      head :unprocessable_entity
    end
  end

  # == [GET] /api/v1/users/passwords/{user_id}.json
  # Get a password reset user
  # ==== Required
  # * id - the users id (implied in URL)
  # * reset_token - the users reset token
  # ==== Returns
  # * 200 - success - returns the user
  # * 404 - failure - invalid reset token
  def show
    @user = find_user

    render json: @user
  end

  # == [PUT] /api/v1/users/passwords/{user_id}.json
  # Update a users password from reset token
  # ==== Required
  # * id - the users id (implied in URL)
  # * reset_token - the users reset token
  # * password - the users new password
  # ==== Returns
  # * 200 - success - returns the user
  # * 404 - failure - invalid reset token
  # * 422 - failure - array of user errors
  def update
    @user = find_user

    if @user.update(password: allowed_params[:password])
      # Remove the reset token and reset all user sessions
      @user.update_columns(authentication_token: nil, reset_token: nil)

      # Unlock their account if its locked
      @user.unlock!

      render json: @user
    else
      render json: @user, status: :unprocessable_entity
    end
  end

  private

  def find_user
    raise ActiveRecord::RecordNotFound if allowed_params[:reset_token].blank?

    User.find_by!(id: allowed_params[:id],
                  reset_token: allowed_params[:reset_token])
  end

  def allowed_params
    params.permit(:id, :email, :reset_token, :password)
  end
end
