class Api::V1::Users::PaymentLogsController < Api::V1::BaseController
  before_action :load_and_authenticate_user

  include DateFiltering

  # == [GET] /api/v1/users/:user_id/payment_logs.json
  # Returns a list of payins/payouts for a user
  # ==== Required
  # * user_id - the id of the user (implied in URL)
  # ==== Optional (Both Required)
  # * start_date - filter results by start date
  # * end_date - filter results by end date
  # ==== Returns
  # * 200 - success - returns an array of _PaymentLog_
  def index
    @payment_logs = PaymentLog.where(user: @user)
                              .includes(dividend: :property)
                              .order(created_at: :desc)

    @payment_logs = @payment_logs.where(created_at: @start_date..@end_date) if @start_date && @end_date

    render json: @payment_logs, include: { dividend: [:property] }
  end
end
