class Api::V1::Users::RegistrationsController < Api::V1::BaseController
  # == [POST] /api/v1/users/registrations.json
  # Create a new user account
  # ==== Required
  # * email - the users email
  # * password - the users password
  # * password_confirmation - the users password confirmation
  # * marketing - whether the user consents to marketing
  # ==== Optional
  # * type - the user type (legal || natural) [defaults to natural]
  # ==== Returns
  # * 201 - success - returns the user
  # * 400 - failure - returns an array of user validation errors
  def create
    @user = klass.new(allowed_params)
    if persist_user?
      @user.confirm! if allowed_params[:google_uid].present? || allowed_params[:apple_uid].present?
      @user.ensure_confirmation_token!
      @user.ensure_authentication_token!
      @user.login_success!(params[:ip_address], params[:user_agent])
      if allowed_params[:google_uid].blank? && allowed_params[:apple_uid].blank?
        UserPendingDelivery.notify(:registration, @user)
      end

      ph_properties = { email: @user.email, user_type: params[:type] }
      capture_posthog(distinct_id: @user.id, event_name: :account_created, event_properties: ph_properties)

      render json: @user, status: :created
    else
      render json: @user, status: :unprocessable_entity
    end
  end

  private

  def klass
    params[:type] && params[:type] == 'legal' ? User::Legal : User::Natural
  end

  def persist_user?

    if allowed_params[:apple_uid].present? && params[:apple_token]
      valid_token = Omni::AppleTokenValidator.new(id_token: params[:apple_token], provider: 'apple').call
      return valid_token && @user.save
    end

    if allowed_params[:google_uid].present? && params[:google_token].present?
      valid_token = Omni::GoogleTokenValidator.new(token: params[:google_token], provider: 'google',
                                                   uid: allowed_params[:google_uid]).call
      return valid_token && @user.save
    end

    @user.save if allowed_params[:google_uid].blank? && allowed_params[:apple_uid].blank?
  end

  def allowed_params
    params.permit(:email, :password, :password_confirmation, :referer,:marketing,
                  :google_uid, :first_name, :last_name, :apple_uid,
                  :business_name, :business_number, :legal_type)
  end
end
