require 'ibandit'
class Api::V1::Users::BankAccountsController < Api::V1::BaseController
  before_action :load_and_authenticate_user
  before_action :check_user_in_kyc_state

  def index
    render json: RemoteBankAccount.where(bankable: @user)
  end

  def create
    Pundit.authorize(@user, :bank_account, :create?)
    @bank_account = @user.build_remote_bank_account(owner_name: @user.full_name,
                                                      sort_code: allowed_params[:sort_code],
                                                      account_number: allowed_params[:account_number],
                                                      iban: allowed_params[:iban],
                                                      status: 'ACTIVE')
    if @bank_account.save
      BankDelivery.notify(:new_bank_notification, @user)
      render json: @bank_account, status: :created
    else
      render json: { errors: @bank_account.errors }, status: :unprocessable_entity
    end
  rescue Pundit::NotAuthorizedError => e
    msg = 'You can not have more than ONE bank accounts.'
    notifier(msg: msg)
    super
  rescue => e
    render json: { errors: { base: [e.message] } }, status: :unprocessable_entity
  end

  def show
    @bank_account = Mango::BankAccount.find_by_id(@user, params[:id])

    render json: @bank_account
  rescue MangoPay::ResponseError
    head :not_found
  end

  def deactivate
    @bank_account = Mango::BankAccount.find_by_id(@user, params[:id])

    # Cancel any mandates
    @user.mandates.where(bank_account_id: @bank_account.id, cancelled_at: nil).each do |mandate|
      mandate.cancel!
    end

    # Deactivate the bank account
    @bank_account = Mango::BankAccount.deactivate(@user, params[:id])

    render json: @bank_account
  rescue MangoPay::ResponseError
    head :not_found
  end

  def verify_new_account_policy
    # new policy, one bank account for each user
    render json: { policy_response: @user.remote_bank_account.blank? }, status: :ok
  end

  def notifier(msg: nil)
    BankAccountNotifier.with(record: @user, msg: msg || params[:msg]).deliver
  end

  private

  def allowed_params
    params.permit(:owner_name, :sort_code, :account_number, :iban)
  end
end
