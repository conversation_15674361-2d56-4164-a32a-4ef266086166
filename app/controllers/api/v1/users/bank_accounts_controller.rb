require 'ibandit'
class Api::V1::Users::BankAccountsController < Api::V1::BaseController
  before_action :load_and_authenticate_user, except: [:webhook_consent_active]
  before_action :check_user_in_kyc_state, except: [:webhook_consent_active]

  def index
    render json: RemoteBankAccount.where(bankable: @user)
  end

  def banks
    response = ::Nuapay::Banks::ListService.new(params: { supportedcurrencies: 'GBP' }).call
    render json: response.body['data']
  end

  def create
    Pundit.authorize(@user, :bank_account, :create?)
    
    if allowed_params[:bank_id].present?
      consent_params = create_consent_params(allowed_params[:bank_id])
      response = ::Nuapay::OpenBanking::CreateConsentService.new(params: consent_params).call
      
      if response.success?
        render json: response.body['data'], status: :created
      else
        render json: { errors: { base: [response.error] } }, status: :unprocessable_entity
      end
    else
      @bank_account = @user.build_remote_bank_account(owner_name: @user.full_name,
                                                        sort_code: allowed_params[:sort_code],
                                                        account_number: allowed_params[:account_number],
                                                        iban: allowed_params[:iban],
                                                        status: 'ACTIVE')
      if @bank_account.save
        BankDelivery.notify(:new_bank_notification, @user)
        render json: @bank_account, status: :created
      else
        render json: { errors: @bank_account.errors }, status: :unprocessable_entity
      end
    end
  rescue Pundit::NotAuthorizedError => e
    msg = 'You can not have more than ONE bank accounts.'
    notifier(msg: msg)
    super
  rescue => e
    render json: { errors: { base: [e.message] } }, status: :unprocessable_entity
  end

  def show
    @bank_account = Mango::BankAccount.find_by_id(@user, params[:id])

    render json: @bank_account
  rescue MangoPay::ResponseError
    head :not_found
  end

  def deactivate
    @bank_account = Mango::BankAccount.find_by_id(@user, params[:id])

    # Cancel any mandates
    @user.mandates.where(bank_account_id: @bank_account.id, cancelled_at: nil).each do |mandate|
      mandate.cancel!
    end

    # Deactivate the bank account
    @bank_account = Mango::BankAccount.deactivate(@user, params[:id])

    render json: @bank_account
  rescue MangoPay::ResponseError
    head :not_found
  end

  def verify_new_account_policy
    # new policy, one bank account for each user
    render json: { policy_response: @user.remote_bank_account.blank? }, status: :ok
  end

  def authorization_callback
    render json: { status: 'received' }, status: :ok
  end

  def webhook_consent_active
    consent_id = params[:resourceId]
    
    # Fetch consent details
    consent_response = ::Nuapay::OpenBanking::RetrieveConsentService.new(consent_id: consent_id).call
    return render json: {}, status: :unprocessable_entity unless consent_response.success?
    
    # Fetch account details using consent
    accounts_response = ::Nuapay::OpenBanking::ListAccountsService.new(consent_id: consent_id).call
    return render json: {}, status: :unprocessable_entity unless accounts_response.success?
    
    consent_data = consent_response.body['data']
    accounts_data = accounts_response.body['data']
    
    # TODO: IMPLEMENT IN CORE - User identification and bank account creation
    # 
    # The consent_id to user relationship is stored in another repository.
    # This webhook handler needs to be enhanced to:
    # 
    # 1. Query the consent record from the other service/repo using consent_id
    # 2. Extract the associated user_id from that consent record
    # 3. Find the User in this system using the user_id
    # 4. Create RemoteBankAccount for that user with the fetched account data
    # 
    # Implementation steps:
    # - Add service/method to retrieve user_id by consent_id from other repo
    # - Map account data fields from Nuapay response to RemoteBankAccount attributes
    # - Handle cases where user or account data is missing/invalid
    # - Ensure idempotency (don't create duplicate bank accounts)
    # 
    # Expected flow:
    # user = find_user_by_consent_id(consent_id)
    # if user && accounts_data&.any?
    #   account = accounts_data.first
    #   existing_account = user.remote_bank_account
    #   
    #   if existing_account.blank?
    #     bank_account = user.build_remote_bank_account(
    #       owner_name: account['ownerName'] || user.full_name,
    #       iban: account['identification']['iban'],
    #       sort_code: extract_sort_code_from_iban(account['identification']['iban']),
    #       account_number: extract_account_number_from_iban(account['identification']['iban']),
    #       status: 'ACTIVE',
    #       remote_id: consent_id
    #     )
    #     
    #     if bank_account.save
    #       BankDelivery.notify(:new_bank_notification, user)
    #       Rails.logger.info "✅ Bank account created for user #{user.id} with consent #{consent_id}"
    #     else
    #       Rails.logger.error "❌ Failed to create bank account: #{bank_account.errors.full_messages}"
    #     end
    #   else
    #     Rails.logger.info "ℹ️ User #{user.id} already has a bank account, skipping creation"
    #   end
    # end
    
    Rails.logger.info "👉👉👉 Consent Active webhook received for consent: #{consent_id} 👈👈👈"
    Rails.logger.info "Consent data: #{consent_data}"
    Rails.logger.info "Accounts data: #{accounts_data}"
    
    render json: {}, status: :ok
  rescue StandardError => e
    Airbrake.notify(e.message)
    Rails.logger.error "❌ Webhook consent active error: #{e.message}"
    render json: {}, status: :unprocessable_entity
  end

  def notifier(msg: nil)
    BankAccountNotifier.with(record: @user, msg: msg || params[:msg]).deliver
  end

  private

  def allowed_params
    params.permit(:owner_name, :sort_code, :account_number, :iban, :bank_id)
  end

  def create_consent_params(bank_id)
    {
      "bankId": bank_id,
      "integrationType": "SELF_HOSTED",
      "merchantPostAuthUrl": "#{Rails.application.secrets.frontend_url}/users/bank_accounts/authorization_callback",
      "language": "en",
      "countryCode": "GB",
      "accessPermissions": [
        "ACCOUNT_DETAILS_READ"
      ]
    }
  end
end
