class Api::V1::Users::CertificationsController < Api::V1::BaseController
  before_action :load_and_authenticate_user
  before_action :check_user_state

  def create
    @user.assign_attributes(allowed_params)

    if save_user
      @user.certification_attempts.create(state: 'success', certification_level_id: allowed_params[:certification_level_id])

      render json: @user, status: :created
    else
      render json: @user, status: :unprocessable_entity
    end
  end

  def failed
    @user.certification_attempts.create(state: 'fail', certification_level_id: allowed_params[:certification_level_id])
    @user.certification_failed!

    render json: @user, status: :created
  end

  private

  def save_user
    if @user.recertification_required?
      @user.recertified!
    elsif recertifying_user?(@user)
      @user.recertify
      @user.recertified!
    else
      @user.self_certify!
    end
  end

  def recertifying_user?(user)
    [:self_certified,
     :kyc_regular_is_required,
     :kyc_regular_is_pending,
     :kyc_regular_is_complete,
     :kyc_regular_has_failed].include?(user.aasm_state.to_sym)
  end

  def check_user_state
    render json: @user, status: :created
  end

  def allowed_params
    params.permit(:certification_level_id)
  end
end
