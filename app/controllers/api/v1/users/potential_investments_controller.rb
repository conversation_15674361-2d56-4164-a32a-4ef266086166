class Api::V1::Users::PotentialInvestmentsController < Api::V1::BaseController
  before_action :load_and_authenticate_user

  def create
    @potential_investment = PotentialInvestment.new(allowed_params)
    @potential_investment.user = @user

    if @potential_investment.save
      capture_posthog(distinct_id: @user.id, event_name: :investment_created, event_properties: allowed_params.to_h)

      render json: @potential_investment, status: :created, include: '**'
    else
      ph_properties = { properties: allowed_params.to_h, error: @potential_investment.errors&.full_messages&.to_sentence }
      capture_posthog(distinct_id: @user.id, event_name: :investement_failed, event_properties: ph_properties)

      render json: @potential_investment, status: :unprocessable_entity, include: '**'
    end
  end

  def show
    @potential_investment = @user.potential_investments.find(params[:id])

    render json: @potential_investment, include: '**'
  end

  def invest
    @potential_investment = @user.potential_investments.find(params[:id])
    ph_properties = {}
    ph_properties[:investment] = @potential_investment.items.map do |item|

      property = item.property
      sub_total = (property.share_price * item.quantity).floor
      stamp_duty = property.funded? ? 0 : (sub_total * 0.005).ceil
      transaction_fee = (sub_total * 0.02).ceil
      fee = transaction_fee + stamp_duty


      { potential_investment_id: params[:id],
        property_id: item.property_id,
        property_name: property.name,
        quantity: item.quantity,
        type: property.type,
        sub_total: sub_total,
        stamp_duty: stamp_duty,
        transaction_fee: transaction_fee,
        fee: fee }
    end

    if @potential_investment
      if @user.kyc_regular_is_pending?
        User::QueuedActionInvestment.create(target: @potential_investment,
                                            user: @user)

        capture_posthog(distinct_id: @user.id, event_name: :investment_completed, event_properties: ph_properties)

        render json: @potential_investment, status: :created
      elsif @potential_investment.invest!
        capture_posthog(distinct_id: @user.id, event_name: :investment_completed, event_properties: ph_properties)

        render json: @potential_investment, status: :created
      else
        ph_properties[:error] = 'Insufficient funds'
        capture_posthog(distinct_id: @user.id, event_name: :investement_failed, event_properties: ph_properties)

        render json: @potential_investment, status: :unprocessable_entity
      end
    else
      head :not_found
    end
  end

  private

  def allowed_params
    params.permit(items_attributes: [:id, :property_id, :quantity, :_destroy])
  end
end
