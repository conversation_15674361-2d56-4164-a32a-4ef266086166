class Api::V1::Users::DashboardTwosController < Api::V1::BaseController
  before_action :load_and_authenticate_user

  def show
    @dashboard = User::Dashboard.new(@user, property_ids)

    includes = [investments: [:share_orders, :share_logs, :dividends, property: [:photos, :news_items, :tags]]]

    render json: @dashboard, include: includes, serializer: User::DashboardTwoSerializer
  end

  private

  def property_ids
    if params[:kind] == 'cause'
      Property::Cause.ids
    else
      Property.where.not(type: 'Property::Cause').ids
    end
  end
end
