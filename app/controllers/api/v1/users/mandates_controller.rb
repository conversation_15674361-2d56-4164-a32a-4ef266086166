class Api::V1::Users::MandatesController < Api::V1::BaseController
  before_action :load_and_authenticate_user
  before_action :check_user_in_kyc_state

  def index
    render json: @user.mandates
  end

  def show
    @mandate = fetch_mandate

    render json: @mandate
  end

  def update
    @mandate = fetch_mandate

    if @mandate.update(update_params)
      render json: @mandate, status: :ok
    else
      render json: @mandate, status: :unprocessable_entity
    end
  end

  def cancel
    @mandate = Mandate.find_by(user: @user, id: params[:id])

    if @mandate.cancel!
      render json: @mandate
    else
      render json: @mandate, status: :unprocessable_entity
    end
  end

  private

  def fetch_mandate
    Mandate.find_by!(user: @user, id: params[:id])
  end

  def create_params
    params.permit(:amount, :bank_account_id, :day)
  end

  def update_params
    params.permit(:amount, :day)
  end
end
