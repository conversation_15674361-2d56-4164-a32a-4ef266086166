class Api::V1::Users::KycsController < Api::V1::BaseController
  before_action :load_and_authenticate_user, except: [:webhook, :shufti_verification_callback, :initiate_shufti_verification]
  before_action :check_user_state, except: [:check, :webhook, :doc_verification_url, :shufti_verification_callback, :initiate_shufti_verification]
  before_action :set_level, except: [:check, :webhook, :doc_verification_url, :shufti_verification_callback, :create_reference, :initiate_shufti_verification]

  def create
    @user.assign_attributes(allowed_params)
    if params[:level] == 'light'
      success_event_name = :kyc_light_complete
      failure_event_name = :kyc_light_failed
      ph_properties = { kyc_level: params[:level] }
    else
      success_event_name = :kyc_regular_complete
      failure_event_name = :kyc_regular_failed
      ph_properties = { kyc_level: params[:level], occupation: allowed_params[:occupation],
                        income_range: User::Natural::INCOME_RANGES.find { |arg| arg[1] == allowed_params[:income_range].to_i }  }
    end
    spam_checker
    return if performed?
    if @user.send(@save_method)
      capture_posthog(distinct_id: @user.id, event_name: success_event_name, event_properties: ph_properties)
      render json: @user, status: :created
    else
      ph_properties.merge!(errors: @user.errors&.full_messages&.to_sentence)
      capture_posthog(distinct_id: @user.id, event_name: failure_event_name, event_properties: ph_properties)
      Airbrake.notify("#{failure_event_name}  #{@user.errors&.full_messages&.to_sentence}", allowed_params)
      kyc_regular_failed_notify(params[:level])

      render json: @user, status: :unprocessable_entity
    end

  rescue Exception => error
      @user.errors.add(:base, error.message)
      render json: @user, status: :unprocessable_entity
  end

  def create_reference
    require 'uuid7'
    reference = UUID7.generate
    doc = User::ShuftiKycDocument.create(user: @user, kyc_document_id: reference, state: 'initialized')
    if doc.persisted?
      render json: { reference: doc.kyc_document_id }, status: :created
    else
      render json: { errors: doc.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def check
    if @user.over_kyc_limitations?(credit: check_params[:credit].to_i || 0)
      head :unprocessable_entity
    else
      head :ok
    end
  end

  def webhook
    begin
      ActiveRecord::Base.transaction do
        if params[:event_type] == 'KYC_OUTDATED'
          kyc_document = MangoPay::KycDocument.fetch(nil, params[:resource_id])
          user = User.find_by(mangopay_id: kyc_document['UserId'])
          user.update(aasm_state: 'kyc_regular_is_required')
          document = User::KycDocument.find_by(kyc_document_id: params[:resource_id])
          document.update(state: 'OUT_OF_DATE')
          KYCDelivery.notify(:document_outdated, user)
        end
        if params[:event_type] == 'USER_KYC_LIGHT'
          user = User.find_by(mangopay_id: params[:resource_id])
          user.mangopay_kyc_documents.each(&:update_status)
          return if user.valid_kyc_documents?

          user.update(aasm_state: 'kyc_regular_is_required')
          KYCDelivery.notify(:document_outdated, user)
        end
      end
    rescue Exception => e
      ActiveRecord::Rollback
      Airbrake.notify(e)
    end
    Airbrake.notify(params)
  end

  def doc_verification_url
    res = ::Shufti::DocumentVerificationUrlService.new(user: @user, source: params[:source]).call
    if res[:success]
      render json: res[:data], status: :ok
    else
      render json: res[:error], status: :unprocessable_entity
    end
  end

  def shufti_verification_callback
    20.times { Rails.logger.info "👉👉👉 Shufti callback. #{params} 👈👈👈" }
    local_doc = User::ShuftiKycDocument.find_by(kyc_document_id: params[:reference])
    local_doc.update_status if local_doc.present?
    render json: {}, status: :ok
  end

  def initiate_shufti_verification
    user_id = Base64.urlsafe_decode64(params[:token])
    @user = User.find(user_id)
    res = ::Shufti::DocumentVerificationUrlService.new(user: @user).call
    if res[:success]
      render json: res[:data], status: :ok
    else
      render json: res[:error], status: :unprocessable_entity
    end
  rescue Exception => error
    render json: error.message, status: :unprocessable_entity
  end

  private

  def check_user_state
    head :not_found unless @user.registered? ||
                           @user.kyc_light_is_complete? ||
                           @user.kyc_regular_has_failed? ||
                           @user.kyc_regular_is_required?
  end

  def kyc_regular_failed_notify(level)
    return if level == 'light'

    KYCDelivery.notify(:regular_failure, @user, @user.errors&.full_messages&.to_sentence)
  end

  def set_level
    if params[:level].present?
      case params[:level].downcase
      when 'light'
        @save_method = :kyc_light_complete!
      when 'regular'
        @save_method = :kyc_regular_submitted!
      else
        @user.errors.add(:base, 'Level is invalid')
      end
    else
      @user.errors.add(:base, 'Level must be specified')
    end

    render json: @user, status: :unprocessable_entity if @user.errors.any?
  end

  def allowed_params
    address_params = [:id, :address_number, :address_1, :address_2, :city, :country, :date_of_birth,
                      :first_name, :kind, :last_name, :post_code, :region, :_destroy]

    params.permit(:articles_of_association, :business_name, :business_number, :call_me,
                  :country_of_residence, :date_of_birth, :employment_status, :experience, :first_name,
                  :identity_proof, :income_range, :last_name, :legal_type, :middle_name, :nationality,
                  :occupation, :phone_number, :planned_investment, :registration_proof,
                  :shareholder_declaration, :title,
                  address_attributes: address_params,
                  headquarters_attributes: address_params,
                  directors_attributes: address_params,
                  shareholders_attributes: address_params)
  end

  def check_params
    params.permit(:credit, :debit)
  end

  def spam_checker
    spam_user = ::Users::SpamChecker.new(@user.date_of_birth, @user.first_name, @user.last_name).call
    return unless spam_user

    Airbrake.notify("Spam user blocked", allowed_params)
    render json: @user, status: :unprocessable_entity
  end
end
