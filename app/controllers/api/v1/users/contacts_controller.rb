class Api::V1::Users::ContactsController < Api::V1::BaseController
  before_action :load_and_authenticate_user

  def create
    if allowed_params[:email_body].present?
      ContactDelivery.notify(:contact_us, @user, allowed_params[:email_body], allowed_params[:email_subject])

      render json: { message: I18n.t('contact.success', email: @user.email) }, status: :ok
    else
      return render json: { error: I18n.t('contact.fail') }, status: :unprocessable_entity
    end
  end

  private

  def allowed_params
    params.permit(:email_body, :email_subject)
  end
end
