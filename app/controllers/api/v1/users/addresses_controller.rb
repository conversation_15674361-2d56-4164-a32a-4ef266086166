class Api::V1::Users::AddressesController < Api::V1::BaseController
  before_action :load_and_authenticate_user

  def create
    @user_address = @user.build_address(allowed_params)
    if @user_address.save
      UserAddressDelivery.notify(:change_address_notification, @user)
      ChangeAddressNotifier.with(record: @user, msg: "Address has been updated by user.").deliver
      render json: @user.address
    else
      render json: @user.address, status: :unprocessable_entity
    end
  end

  def update
    if @user.address.update(allowed_params)
      UserAddressDelivery.notify(:change_address_notification, @user)
      ChangeAddressNotifier.with(record: @user, msg: "Address has been updated by user.").deliver
      render json: @user.address
    else
      render json: @user.address, status: :unprocessable_entity
    end
  end

  private

  def allowed_params
    params.permit(:address_number, :address_1, :address_2, :city, :region, :post_code, :country)
  end
end
