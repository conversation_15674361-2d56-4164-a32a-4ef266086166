class Api::V1::Users::SessionsController < Api::V1::BaseController
  before_action :load_user, only: [:show, :destroy]

  def create
    @user = User.where( "email = :email OR google_uid = :google_uid OR apple_uid = :apple_uid",
                        email: allowed_params[:email], google_uid: allowed_params[:google_uid],
                        apple_uid: allowed_params[:apple_uid]).first
    return head :unprocessable_entity if @user.blank?

    if @user&.locked?
      head :locked
      return
    end

    if authenticate_user?
      return login_success if @user.factor_id.blank? || omni_auth?
      return render json: @user, status: :not_acceptable if allowed_params[:otp].blank?

      res = Twilio::Totp::Challenge.new(user_id: @user.id, factor_id: @user.factor_id, otp: allowed_params[:otp]).call
      return login_success if res[:success] && res[:data].status == 'approved'

      render json: { factor_id: @user.factor_id, password: allowed_params[:password] }.to_json, status: :not_acceptable
    else
      @user.login_failure!(allowed_params[:ip_address], allowed_params[:user_agent])
      account_lock_alert

      head :unprocessable_entity
    end
  end

  def show
    render json: @user
  end

  def destroy
    @user.update_column(:authentication_token, nil)

    capture_posthog(distinct_id: @user.id, event_name: :signed_out)

    head :ok
  end

  private

  def load_user
    if request.headers['Authorization'].present?
      authenticate_with_http_token do |token|
        @user = User.authenticate_from_token(params[:id], token)
      end
    end

    head :unauthorized unless @user
  end

  def allowed_params
    params.permit(:email, :ip_address, :password, :user_agent, :otp, :google_uid, :google_token, :apple_uid, :apple_token)
  end

  def exceeded_limit_alert
    login_count = @user.login_attempts.where('created_at >= ?', Time.zone.now.beginning_of_week).count
    return if login_count <= 20

    LoginAttemptNotifier.with(record: @user, msg: "Exceeded maximum login attempts").deliver
  end

  def new_country_login_alert
    ip = allowed_params[:ip_address]
    return if ip.blank? || Rails.env.development?
    login_attempts = @user.login_attempts
    return if login_attempts.blank?
    return if login_attempts.where(ip: ip).any?

    current_country = search_country(ip)
    return if current_country.blank?
    return unless login_attempts.pluck(:ip).uniq.map { |ip| search_country(ip) }.include?(current_country)

    UserLoginDelivery.notify(:new_login_notification, @user)
  end

  def search_country(ip)
    results = Geocoder.search(ip)
    results.first&.country
  end

  def unique_ips_threshold_alert
   login_attempts = @user.login_attempts.where('created_at >= ?', 30.days.ago)
   return if login_attempts.blank? || login_attempts.pluck(:ip).uniq.count < 10

   LoginAttemptNotifier.with(record: @user, msg: "Above 10 unique IP address in 30 days").deliver
  end

  def account_lock_alert
    return if @user.failed_attempts < 3

    UserLoginDelivery.notify(:failed_attempts_notification, @user)
    LoginAttemptNotifier.with(record: @user, msg: "experienced three consecutive failed login attempts").deliver
  end

  def authenticate_user?
    if allowed_params[:apple_uid].present?
      valid_token = Omni::AppleTokenValidator.new(id_token: allowed_params[:apple_token], provider: 'apple').call
      return valid_token && @user.apple_uid == allowed_params[:apple_uid]
    end

    if allowed_params[:google_uid].present?
      return Omni::GoogleTokenValidator.new(token: allowed_params[:google_token], provider: 'google',
                                            uid: allowed_params[:google_uid]).call
    end

    @user && @user.authenticate(allowed_params[:password])
  end

  def login_success
    new_country_login_alert
    @user.login_success!(allowed_params[:ip_address], allowed_params[:user_agent])
    @user.ensure_authentication_token!
    @user.update(google_uid: allowed_params[:google_uid]) if allowed_params[:google_uid].present?
    @user.update(apple_uid: allowed_params[:apple_uid]) if allowed_params[:apple_uid].present?
    exceeded_limit_alert
    unique_ips_threshold_alert
    capture_posthog(distinct_id: @user.id, event_name: :signed_in)
    render json: @user, status: :created
  end

  def omni_auth?
    (allowed_params[:apple_uid] || allowed_params[:google_uid]).present?
  end
end
