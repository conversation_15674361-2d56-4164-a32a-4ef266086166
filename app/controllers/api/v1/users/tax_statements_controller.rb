class Api::V1::Users::TaxStatementsController < Api::V1::BaseController
  before_action :load_and_authenticate_user

  include DateFiltering

  def show
    @tax_statement = User::TaxStatement.new(user: @user,
                                            start_date: @start_date,
                                            end_date: @end_date)

    if @tax_statement.valid?
      render json: @tax_statement, include: { items: [:property] }
    else
      head :unprocessable_entity
    end
  end
end
