class Api::V1::Users::SettingsController < Api::V1::BaseController
  before_action :load_and_authenticate_user
  before_action :validate_old_password, only: [:update_password]
  before_action :validate_confirm_password, only: [:update_password]
  before_action :international_phone_format, only: [:create]
  before_action :otp_verification, only: [:update_password]

  def create
    if @user.update(allowed_params)
      render json: @user
    else
      render json: @user, status: :unprocessable_entity
    end
  end

  def update_email
    if @user.authenticate(update_email_params[:password]).blank?
      return render json:  { error: I18n.t('update_email.auth_fail') },status: :unprocessable_entity
    end

    if update_email_params[:email].blank?
      return render json: { error: I18n.t('update_email.invalid_email') }, status: :unprocessable_entity
    end

    @user.ensure_confirmation_token!
    @user.update(email: update_email_params[:email], confirmed_at: nil)
    UserPendingDelivery.notify(:registration, @user)

    render json: { message: I18n.t('update_email.success') }, status: :ok
  end

  def update_password
    if @user.update(password: update_email_params[:password])
      capture_posthog(distinct_id: @user.id, event_name: :new_password_set)

      render json: { message: I18n.t('update_password.success') }, status: :ok
    else
      render json: @user, status: :unprocessable_entity
    end
  end

  private

  def validate_old_password
    password = update_password_params[:password]
    user = @user.authenticate(update_password_params[:old_password])
    return if password.present? && user.present?

    @user.errors.add(:old_password, 'is incorrect')
    render json: @user, status: :unprocessable_entity
  end

  def validate_confirm_password
    return if update_password_params[:password] == update_password_params[:password_confirmation]

    @user.errors.add(:password_confirmation, 'is incorrect')
    render json: @user, status: :unprocessable_entity
  end

  def international_phone_format
    return if allowed_params[:phone_number].blank? || @user.phone_verified?

    phone_number = allowed_params[:phone_number]&.phony_formatted(strict: true, format: :international)
    return if phone_number.present?

    @user.errors.add(:phone_number, 'is incorrect')
    render json: @user, status: :unprocessable_entity
  end

  def allowed_params
    address_params = [:id, :address_number, :address_1, :address_2, :city, :country, :post_code, :_destroy]
    user_params = params.permit(:phone_number)
    address_params = params.permit(users_address: address_params)
    user_params.merge!(address_attributes: address_params[:users_address])
  end

  def update_email_params
    params.permit(:email, :password)
  end

  def update_password_params
    params.permit(:old_password, :password, :password_confirmation, :otp)
  end

  def otp_verification
    return if @user.factor_id.blank?

    response = ::Twilio::Totp::Challenge.new(user_id: @user.id, factor_id: @user.factor_id,
                                             otp: update_password_params[:otp]).call
    return if response[:success] && response[:data].status == 'approved'

    @user.errors.add(:base, 'OTP is incorrect')
    render json: @user, status: :unprocessable_entity
  end
end

