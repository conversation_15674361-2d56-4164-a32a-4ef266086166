class Api::V1::Users::ConfirmationsController < Api::V1::BaseController
  before_action :load_and_authenticate_user, only: :sca_enroll

  def update
    @user = User.find_by!(id: allowed_params[:id],
                          confirmation_token: allowed_params[:confirmation_token])

    @user.confirm!
    @user.save

    render json: @user
  end

  def resend
    @user = User.find_by!(email: allowed_params[:email], confirmed_at: nil)

    @user.ensure_confirmation_token!
    UserPendingDelivery.notify(:registration, @user)
    head :ok
  end

  def sca_enroll
    resp = MangoPay.request('post', "#{MangoPay.api_path}/sca/users/#{@user.mangopay_id}/enrollment")
    render json: resp, status: :ok
  rescue Exception => e
    Airbrake.notify(e.message)
    render json: { errors: { base: [e.message] } }, status: :unprocessable_entity
  end

  def webhook
    Airbrake.notify(params)
    user = User.where('id = ? OR mangopay_id = ?', params[:user_id], params[:mangopay_id]).first
    if params[:event_type] == 'USER_ACCOUNT_ACTIVATED' || (params[:actionStatus] == 'SUCCEEDED' && params[:controlStatus] == 'VALIDATED')
      unless user.sca_status
        user.update_column(:sca_status, true)
        ScaMailer.success(user).deliver_later
      end
      render json: user, status: :ok
    else
      ScaMailer.failure(user).deliver_later
      render json: { errors: { base: ['SCA verification failed.'] } }, status: :unprocessable_entity
    end
  end

  private

  def allowed_params
    params.permit(:id, :confirmation_token, :email)
  end
end
