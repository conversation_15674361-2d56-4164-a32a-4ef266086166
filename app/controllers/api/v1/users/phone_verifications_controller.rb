# frozen_string_literal: true

module Api
  module V1
    module Users
      class PhoneVerificationsController < Api::V1::BaseController
        before_action :load_and_authenticate_user
        def create
          phone_number = params[:phone_number]
          phone_number = phone_number.phony_formatted(strict: true, format: :international)
          return error(I18n.t('invalid_phone')) if phone_number.blank?

          @user.update(phone_number: phone_number)
          response = Telnyx::SendOtp.new(phone_number: phone_number).call
          if response.success? && @user.update(telnyx_id: JSON.parse(response.body)['data']['id'], phone_number: params[:phone_number])
            success(@user)
          else
            error(error_msg(response))
          end
        end

        def verify_otp
          response = Telnyx::AcceptOtp.new(phone_number: @user.phone_number, code: params[:otp]).call
          return error(error_msg(response)) unless response.success?
          return error(I18n.t('invalid_opt')) unless JSON.parse(response.body)['data']['response_code'] == 'accepted'

          return success(@user) if @user.update(phone_verified: true)
          error(@user.errors.full_messages.to_sentence)
        end

        def show
          response = Telnyx::Verification.new(@user.telnyx_id).call
          if response.success?
            success(JSON.parse(response.body))
          else
            error(error_msg(response))
          end
        end

        private

        def error_msg(response)
          JSON.parse(response.body)['errors'][0]['detail']
        end
      end
    end
  end
end
