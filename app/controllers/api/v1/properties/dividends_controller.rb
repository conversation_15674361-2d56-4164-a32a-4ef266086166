class Api::V1::Properties::DividendsController < Api::V1::BaseController
  before_action :load_and_authenticate_user

  include DateFiltering

  def index
    @dividends = @user.dividends
                      .includes(property: [:documents, :floorplans, :photos, :tags])
                      .order(created_at: :desc)

    @dividends = @dividends.where(created_at: @start_date..@end_date) if @start_date && @end_date

    render json: @dividends, include: '**'
  end
end
