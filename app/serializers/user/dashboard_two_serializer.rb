class User::DashboardTwoSerializer < ApplicationSerializer
  attribute :annualised_return, key: :predicted_return
  attribute :profit, key: :profit
  attribute :total_value, key: :total_value
  attribute :return_to_date, key: :return_to_date
  attribute :graphs

  # has_many :investments
  def graphs
    [
      object.monthly_revenue,
      object.calculate_monthly_deposit_withdraw_summary,
      object.user_investments_summary
    ]
  end
end
