class Property::RegularSerializer < ApplicationSerializer
  attributes :id,
             :address_1,
             :address_2,
             :available_shares,
             :certification_level_ids,
             :city,
             :created_at,
             :description,
             :easy_exit,
             :funded,
             :guaranteed_yield,
             :hpi,
             :hpi_area,
             :investment_case_and_risk,
             :name,
             :placeholder,
             :postcode,
             :property_amount,
             :property_fee_deferred_tax,
             :property_fee_legal_and_professional,
             :property_fee_pre_let_expenses,
             :property_fee_repairs_provision,
             :property_fee_stamp_duty,
             :rent_amount,
             :rental_fee_allowance_for_voids,
             :rental_fee_corporation_tax,
             :rental_fee_deferred_fees,
             :rental_fee_insurance,
             :rental_fee_maintenance_allowance,
             :rental_fee_management,
             :rental_fee_spv_charge,
             :rental_yield,
             :share_count,
             :share_price,
             :slug,
             :spv_name,
             :target_amount,
             :thumbnail_label,
             :type

  has_many :documents
  has_many :floorplans
  has_many :legal_documents
  has_many :news_items
  has_many :photos
  has_many :tags

  def rental_yield
    PropertyUtils.rental_yield(object)
  end
end
