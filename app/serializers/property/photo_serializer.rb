class Property::PhotoSerializer < ApplicationSerializer
  attributes :attachment_url, :medium_url, :thumbnail_url

  def attachment_url
    url_for(object.attachment) if object.attachment.attached?
  end

  def medium_url
    url_for(object.attachment.variant(resize: '600x450')) if object.attachment.attached?
  end

  def thumbnail_url
    url_for(object.attachment.variant(resize: '200x150')) if object.attachment.attached?
  end
end
