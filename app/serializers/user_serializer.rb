class UserSerializer < ApplicationSerializer
  attributes :id, :aasm_state, :authentication_token, :business_name,
             :business_number, :call_me, :confirmed_at,
             :country_of_residence, :created_at, :date_of_birth, :email, :employment_status,
             :experience, :first_name, :identity_proof, :income_range,
             :last_name, :legal_type, :marketing, :middle_name, :nationality, :occupation, :phone_number,
             :planned_investment, :previous_state, :reset_token, :title, :type, :updated_at, :errors,
             :phone_verified, :factor_id, :sca_status, :valid_bank?

  has_one :address
  has_one :headquarters

  has_many :directors
  has_many :shareholders
  has_one :remote_bank_account

end
