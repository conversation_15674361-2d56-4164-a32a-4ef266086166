class Users::<PERSON><PERSON><PERSON><PERSON><PERSON>
  FIRST_NAMES = %w[Insch <PERSON><PERSON> bamfo <PERSON> mohammed <PERSON><PERSON>].freeze
  LAST_NAMES = %w[Wilderness <PERSON>yte Fiyaz PERRI MR Feilding <PERSON><PERSON><PERSON> David <PERSON>].freeze
  DOBS = ['16TH aUGUST 1990', '1971 april 28', '12th july 1996', '27/04/1978', '04/08/1998','23/02/1989','15/09/1971','17/08/1978','05/09/2001
'].freeze

  def initialize(dob, first_name, last_name)
    @first_name = first_name&.strip&.squeeze(" ")
    @last_name = last_name&.strip&.squeeze(" ")
    @dob = dob.to_date
  end

  def call
    return true if blank_attributes?
    (scan_name(first_name) &&
      scan_name(last_name) &&
      scan_dob) ||
      (scan_matching(first_name) &&
      scan_matching(last_name))
  end




  private

  attr_accessor :first_name, :last_name, :dob, :post_code

  def blank_attributes?
    first_name.blank? || last_name.blank? || dob.blank?
  end

  def scan_name(name)
    splited_data(name).any? { |name_word| scan_casecmp?(FIRST_NAMES, name_word) || scan_casecmp?(LAST_NAMES, name_word) }
  end

  def scan_matching(name)
    splited_data(name).any? { |name_word| levenshtein_scan?(FIRST_NAMES, name_word) || levenshtein_scan?(LAST_NAMES, name_word)}
  end

  def scan_dob
    DOBS.any? { |date_of_birth| dob.to_date == date_of_birth.to_date }
  end

  def scan_casecmp?(data_set, scan_able)
    data_set.any? { |name| name.casecmp?(scan_able) }
  end

  def levenshtein_scan?(data_set, scan_able)
    data_set.any? { |name| percentage_match(name, scan_able) >= 75 }
  end

  def splited_data(data)
    data.split(' ')
  end

  def levenshtein_distance(str1, str2)
    m, n = str1.length, str2.length
    dp = Array.new(m + 1) { Array.new(n + 1, 0) }

    # Initialize the base cases
    (0..m).each { |i| dp[i][0] = i }
    (0..n).each { |j| dp[0][j] = j }

    # Fill the DP table
    (1..m).each do |i|
      (1..n).each do |j|
        if str1[i - 1] == str2[j - 1]
          dp[i][j] = dp[i - 1][j - 1] # No change needed
        else
          dp[i][j] = 1 + [dp[i - 1][j], dp[i][j - 1], dp[i - 1][j - 1]].min
        end
      end
    end
    dp[m][n]
  end

  def percentage_match(work1, work2)
    max_length = [work1.length, work2.length].max
    distance = levenshtein_distance(work1, work2)
    similarity = (1 - (distance.to_f / max_length)) * 100
    similarity.round(2)
  end
end
