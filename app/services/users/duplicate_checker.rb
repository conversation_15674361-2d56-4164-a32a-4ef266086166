class Users::<PERSON><PERSON><PERSON><PERSON><PERSON>
  def initialize(first_name, last_name, dob, post_code)
    @first_name = first_name&.strip
    @last_name = last_name&.strip
    @dob = dob
    @post_code = post_code&.strip
  end

  def call
    return if blank_attributes?

    User.joins(:addresses)
        .where(users: build_user_params, user_addresses: build_user_address_params)
        .presence
  end

  private

  attr_accessor :first_name, :last_name, :dob, :post_code

  def build_user_params
    {
      first_name: first_name,
      last_name: last_name,
      date_of_birth: dob
    }
  end

  def build_user_address_params
    { 
      post_code: post_code
    }
  end

  def blank_attributes?
    first_name.blank? || last_name.blank? || dob.blank? || post_code.blank?
  end

end