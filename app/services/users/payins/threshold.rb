class Users::Payins::<PERSON><PERSON>esh<PERSON>
  def initialize(user:)
    @user = user
  end

  def call
    per_day_threshold >= 5 || per_week_threshold >= 10 || per_month_threshold >=15
  end

  private

  attr_accessor :user

  def per_day_threshold
    start_of_day = Time.zone.now.beginning_of_day
    end_of_day = Time.zone.now.end_of_day 
    daily_payins_count = user_payments.where(created_at: start_of_day..end_of_day).count
  end

  def per_week_threshold
    start_of_week = Time.zone.now.beginning_of_week
    end_of_week = Time.zone.now.end_of_week
    weekly_payins_count = user_payments.where(created_at: start_of_week..end_of_week).count
  end

  def per_month_threshold
    start_of_month = Time.zone.now.beginning_of_month
    end_of_month = Time.zone.now.end_of_month
    monthly_payins_count = user_payments.where(created_at: start_of_month..end_of_month).count
  end

  def user_payments
    @payments ||= user.payment_logs.where(kind: 'BANK_WIRE', direction: 'credit')
  end

end