# frozen_string_literal: true

# https://developer.nuapay.com/ob_createpayment.html#operation/getRefundUsingGET

module Nuapay
  module Webhooks
    class PaymentCompleteService
      FAILED_PAYMENT_STATUSES = ['SETTLEMENT_REJECTED','CONSENT_API_REJECTED','TIMEOUT','CONSENT_TIMEOUT','UNEXPECTED_ERROR','UNKNOWN']
      def initialize(remote_id:)
        @remote_id = remote_id
      end

      def call
        @res = remote_payment
        payment_log
        @user = @log.user
        process_investment
      rescue StandardError => e
      end

      private
      attr_reader :remote_id, :res, :log, :user

      def remote_payment
        @payment ||= ::Nuapay::Payments::ShowService.new(payment_id: remote_id).call
        raise 'remote payment error.' unless @payment.success?

        @payment.body['data']
      end

      def payment_log
        @log  = PaymentLog.find_by(remote_id: remote_id)
        raise 'Log not found.' if @log.blank?
      end

      def process_investment
        payment_status = res['status']
        if FAILED_PAYMENT_STATUSES.include? payment_status
          log.failure!(payment_status)
        elsif payment_status == 'PAYMENT_RECEIVED'
          save_user_remote_account if user.remote_bank_account.blank?
          log.successful!(payment_status)
        else
          log.status = payment_status
          log.save(validate: false)
        end
      end

      def save_user_remote_account
        user.create_remote_bank_account(owner_name: res['debtorAccount']['name'],
                                        account_number: res['debtorAccount']['identification'][6, 8],
                                        branch_code: res['debtorAccount']['identification'][0, 6],
                                        status: 'ACTIVE')
      end
    end
  end
end
