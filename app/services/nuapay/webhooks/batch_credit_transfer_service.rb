# frozen_string_literal: true

module <PERSON>uapay
  module Webhooks
    class BatchCreditTransferService
      def initialize(remote_id:)
        @remote_id = remote_id
      end

      def call
        process_batch!
      rescue Exception => e
        20.times { Rails.logger.info "👉👉👉 Exception Webhook batch credit transfer . #{e.message} #{e}👈👈👈" }
      end

      private

      attr_reader :remote_id

      def batch_credit_transfer
        @bct ||= ::Nuapay::CreditTransfers::Batch::ShowService.new(batch_ct_id: remote_id).call
        raise 'Error while fetching batch object' unless @bct.success?

        @bct.body['data']
      end

      def credit_transfers
        @cts ||= ::Nuapay::CreditTransfers::Batch::ListCreditTransfersService.new(batch_ct_id: remote_id).call
        raise 'Error while fetching batch CT objects' unless @cts.success?

        @cts.body['data']
      end

      def process_batch!
        # NO Credit Transfers when batch failed
        if batch_credit_transfer['batchStatus'] == 'FAILED'
          logs = PaymentLog.where(remote_id: remote_id)
          dividends = Property::Dividend.find_by(id: logs.pluck(:dividend_id).uniq)
          logs.each { |log| log.failure!('FAILED') }
          error_msg = batch_credit_transfer['rejectDetails']['rejectDescription']
          dividends.update_all(aasm_state: 'failed', reason: error_msg)
        else
          process_credit_transfers_in_batch!
        end
      end

      def accept_credit_transfer!(dividend_id)
        log = PaymentLog.find_by(remote_id: remote_id, dividend_id: dividend_id)
        dividend = log.dividend
        log.successful!('ACCEPTED')
        dividend.complete!
        dividend.send_receipt!
      end

      def reject_or_fail_credit_transfer!(status, dividend_id, reason)
        log = PaymentLog.find_by(remote_id: remote_id, dividend_id: dividend_id)
        dividend = log.dividend
        log.failure!(status)
        dividend.update_columns(reason: reason, aasm_state: 'failed')
      end

      def process_credit_transfers_in_batch!
        credit_transfers.each do |ct|
          status = ct['paymentStatus']
          dividend_id = ct['endToEndId']
          case status
          when 'ACCEPTED'
            accept_credit_transfer!(dividend_id)
          when 'REJECTED'
            reason = ct['rejectDetails']['rejectDescription']
            reject_or_fail_credit_transfer!('REJECTED', dividend_id, reason)
          when 'FAILED'
            reason = ct['rejectDetails']['rejectDescription']
            reject_or_fail_credit_transfer!('FAILED', dividend_id, reason)
          end
        end
      end
    end
  end
end
