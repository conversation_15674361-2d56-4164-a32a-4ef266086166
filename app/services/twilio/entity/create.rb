# frozen_string_literal: true

module Twilio
  module Entity
    class Create < BaseService
      def initialize(user_id:)
        @user_id = user_id
      end

      def call
        entity = TwilioVerifyClient.entities
                                   .create(identity: entity_id)
        success(data: entity)
      rescue Exception => error
        error(message: error.error_message)
      end

      private

      attr_reader :user_id

      def entity_id
        "entity-#{user_id}"
      end
    end
  end
end
