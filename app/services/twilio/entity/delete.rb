# frozen_string_literal: true

module Twilio
  module Entity
    class Delete < BaseService
      def initialize(user_id:)
        @user_id = user_id
      end

      def call
        response = TwilioVerifyClient.entities(entity_id)
                                     .delete
        success(data: response)
      rescue Exception => error
        error(message: error.error_message)
      end

      private

      attr_reader :user_id

      def entity_id
        "entity-#{user_id}"
      end
    end
  end
end
