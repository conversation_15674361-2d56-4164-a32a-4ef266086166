# frozen_string_literal: true

module Twilio
  module Totp
    class Create < BaseService
      def initialize(user:, twilio_entity:)
        @user = user
        @twilio_entity = twilio_entity
      end

      def call
        new_factor = twilio_entity.new_factors.create(friendly_name: user.full_name, factor_type: 'totp')
        success(data: new_factor)
      rescue Exception => error
        error(message: error.error_message)
      end

      private

      attr_reader :user, :twilio_entity
    end
  end
end
