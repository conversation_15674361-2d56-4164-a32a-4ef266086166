# frozen_string_literal: true

module Twilio
  module Totp
    class Verify < BaseService
      def initialize(user_id:, factor_id:, otp:)
        @user_id = user_id
        @factor_id = factor_id
        @otp = otp
      end

      def call
        factor = TwilioVerifyClient.entities(entity_id)
                                   .factors(factor_id)
                                   .update(auth_payload: otp)
        success(data: factor)
      rescue Exception => error
        error(message: error.error_message)
      end

      private

      attr_reader :user_id, :factor_id, :otp

      def entity_id
        "entity-#{user_id}"
      end
    end
  end
end
