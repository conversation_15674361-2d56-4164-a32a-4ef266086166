# frozen_string_literal: true

module Twilio
  module Totp
    class Challenge < BaseService
      def initialize(user_id:, factor_id:, otp:)
        @user_id = user_id
        @factor_id = factor_id
        @otp = otp
      end

      def call
        factor = TwilioVerifyClient.entities(entity_id)
                                   .challenges
                                   .create(
                                     auth_payload: otp,
                                     factor_sid: factor_id
                                   )
        success(data: factor)
      rescue Exception => error
        error(message: error.error_message)
      end

      private

      attr_reader :user_id, :factor_id, :otp

      def entity_id
        "entity-#{user_id}"
      end
    end
  end
end
