require 'uuid7'
class Shufti::CreateLocalReferenceService < BaseService
  def initialize(user:)
    @user = user
  end

  # This service method creates a local Shufti reference record for the user,
  # without contacting Shufti's external API. It simply generates a reference
  # using UUID7 and stores it with a state indicating it's not yet submitted.
  def call
    # Generate a UUID7 reference
    reference = UUID7.generate

    # If you have an existing model for storing shufti references, you can adapt that here.
    # For now, we'll assume it’s stored in the shufti_kyc_documents table.
    doc = User::ShuftiKycDocument.create(user: @user, kyc_document_id: reference, state: 'initialized')
    if doc.persisted?
      success(data: doc)
    else
      error(message: doc.errors.full_messages.join(', '))
    end
  end
end
