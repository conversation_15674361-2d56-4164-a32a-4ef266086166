class Shufti::DocumentVerificationUrlService < BaseService
  def initialize(user:)
    @user = user
  end

  def call
    raise 'You account is verified' if verification_required?

    success(data: find_or_generate_remote_document)
  rescue StandardError => e
    error(message: e.message)
  end

  private

  attr_reader :user
  def last_pending_document
    @pending_doc ||= user.shufti_kyc_documents.initialized.last
  end

  def reference_id
    last_pending_document&.kyc_document_id
  end

  def find_or_generate_remote_document
    return generate_verification if new_request_required?

    last_pending_document
  end

  def remote_event
    @res ||= ::Shufti::StatusService.new(reference: reference_id).call
    raise @res.error['error']['message'] unless  @res.success?
    @res.body['event']
  end

  def generate_verification
    res = ::Shufti::VerificationService.new(user: user).call
    raise res.error['error']['message'] unless res.success?

    res_body = res.body
    user.shufti_kyc_documents.create(state:res_body['event'], kyc_document_id: res_body['reference'], verification_url: res_body['verification_url'])
  end

  def verification_required?
    (last_pending_document.present? && User::ShuftiKycDocument.validated?(remote_event)) ||
    user.kyc_regular_is_complete? || user.kyc_regular_is_pending?
  end

  def new_request_required?
    last_pending_document.blank? || User::ShuftiKycDocument.refused?(remote_event)
  end
end