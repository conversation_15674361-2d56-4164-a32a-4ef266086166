# frozen_string_literal: true

module Telnyx
  class BaseService
    def initialize(url:, request_method: 'get', params: nil, version: 'v2')
      @url = url
      @request_method = request_method
      @params = params
      @version = version
    end

    private

    attr_accessor :url, :request_method, :params, :version

    def telnyx_response
      TelnyxClient.send(request_method) do |req|
        req.url "/#{version}/#{url}"
        req.body = params.to_json if params.present?
      end
    end

    def profile_selection
      response = Telnyx::Profiles::List.new.call
      return unless response.success?

      profile_lists = JSON.parse(response.body)
      selected_list = profile_lists['data'].find { |res| res['name'] == 'phone verification profile' }


      if selected_list.blank?
        selected_list = Telnyx::Profiles::Create.new(name: 'phone verification profile').call
      end

      selected_list['id']
    end
  end
end
