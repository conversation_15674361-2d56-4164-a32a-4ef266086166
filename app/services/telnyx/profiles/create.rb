# frozen_string_literal: true

module Telnyx
  module Profiles
    class Create < Telnyx::BaseService
      def initialize(name:, destination_code: '*')
        @name = name
        @destination_code = [destination_code]
        super(url: url, params: req_body, request_method: 'post')
      end

      def call
        sms_template_id
        return if template_id.blank?

        telnyx_response
      end

      private

      attr_reader :name, :destination_code, :template_id

      def url
        'verify_profiles'
      end

      def req_body
        { name: name,
          sms: {
            messaging_template_id: template_id,
            app_name: 'Phone',
            whitelisted_destinations: destination_code
          } }
      end

      def sms_template_id
        response = Telnyx::Templates::List.new.call
        return unless response.success?

        template = JSON.parse(response.body)
        @template_id = template['data'][0]['id']
      end
    end
  end
end
