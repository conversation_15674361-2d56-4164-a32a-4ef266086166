# frozen_string_literal: true

module Telnyx
  class SendOtp < Telnyx::BaseService
    def initialize(phone_number:, profile_id: nil)
      @phone_number = phone_number
      @profile_id = profile_id
      super(url: url, params: req_body, request_method: 'post')
    end

    def call
      telnyx_response
    end

    private

    attr_accessor :phone_number, :profile_id

    def url
      'verifications/sms'
    end

    def req_body
      { phone_number: phone_number, verify_profile_id: profile_id || profile_selection }
    end
  end
end
