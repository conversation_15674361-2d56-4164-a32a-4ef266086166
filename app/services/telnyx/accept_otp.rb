# frozen_string_literal: true

module Telnyx
  class AcceptOtp < Telnyx::BaseService
    def initialize(phone_number:, code:, profile_id: nil)
      @phone_number = phone_number
      @profile_id = profile_id
      @code = code.strip
      super(url: url, params: req_body, request_method: 'post')
    end

    def call
      telnyx_response
    end

    private

    attr_accessor :phone_number, :profile_id, :code

    def url
      "verifications/by_phone_number/#{phone_number}/actions/verify"
    end

    def req_body
      { code: code, verify_profile_id: profile_id || profile_selection }
    end
  end
end
