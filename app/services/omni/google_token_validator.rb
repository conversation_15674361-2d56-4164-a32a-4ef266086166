# frozen_string_literal: true

module Omni
  class GoogleTokenValidator < BaseService
    def initialize(token:, provider:, uid:)
      super()
      @token = token
      @provider = provider
      @uid = uid
    end

    def call
      valid_token?
    rescue StandardError
      false
    end

    private

    attr_reader :token, :provider, :uid

    def valid_token?
      url = "https://www.googleapis.com/oauth2/v1/userinfo?alt=json&access_token=#{token}"
      response = Faraday.new(url: url).get
      return unless response.status == 200

      response = JSON.parse(response.body)

      uid == response['id']
    end
  end
end
