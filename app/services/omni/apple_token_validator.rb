# frozen_string_literal: true

require 'jwt'
require 'net/http'
require 'json'

module Omni
  class AppleTokenValidator < BaseService
    def initialize(id_token:, provider:)
      super()
      @id_token = id_token
      @provider = provider
    end

    def call
      valid_token?
    rescue StandardError
      false
    end

    private

    attr_reader :id_token, :provider

    def valid_token?
      response = Net::HTTP.get(URI('https://appleid.apple.com/auth/keys'))
      apple_public_keys = JSON.parse(response)['keys']

      # Decode the token without verification to get header info
      header = JWT.decode(id_token, nil, false).second

      # Find the matching key
      matching_key = apple_public_keys.find { |key| key['kid'] == header['kid'] }
      matching_key.present?
    end
  end
end
