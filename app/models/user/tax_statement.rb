class User::TaxStatement < ActiveModelSerializers::Model
  include ActiveModel::Model

  attr_accessor :user, :start_date, :end_date

  validates :user, :start_date, :end_date, presence: true

  def investment_costs
    items.sum(&:investment_costs)
  end

  def sales_proceeds
    items.sum(&:sales_proceeds)
  end

  def total_dividends
    items.sum(&:total_dividends)
  end

  def total_income
    items.sum(&:total_income)
  end

  def total_fees
    items.sum(&:total_fees)
  end

  def subtotal
    items.sum(&:subtotal)
  end

  def items
    return nil unless valid?

    properties.collect do |property|
      share_logs = sell_logs_for_period.select { |sl| sl.property_id == property.id }
      payment_logs = payment_logs_for_period.select { |pl| pl.dividend.property_id == property.id }

      item_class(property).new(user: user,
                               property: property,
                               share_logs: share_logs,
                               payment_logs: payment_logs)
    end
  end

  private

  def item_class(property)
    case property.class.to_s
    when 'Property::Development'
      User::TaxStatementItemDevelopment
    when 'Property::Loan'
      User::TaxStatementItemLoan
    else
      User::TaxStatementItem
    end
  end

  def payment_logs_for_period
    user.payment_logs
        .includes(:dividend)
        .where(kind: 'DIVIDEND')
        .where(created_at: date_range)
        .where.not(dividend_id: nil)
  end

  def sell_logs_for_period
    user.share_logs
        .where(created_at: date_range)
        .where('quantity < 0')
  end

  def properties
    sell_properties = sell_logs_for_period.pluck(:property_id)
    dividend_properties = payment_logs_for_period.pluck('property_dividends.property_id')
    property_ids = (sell_properties + dividend_properties).uniq

    Property.where(id: property_ids)
  end

  def date_range
    start_date.beginning_of_day..end_date.end_of_day
  end
end
