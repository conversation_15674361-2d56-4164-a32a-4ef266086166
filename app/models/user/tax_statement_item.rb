class User::TaxStatementItem < ActiveModelSerializers::Model
  include ActiveModel::Model

  attr_accessor :user, :property, :share_logs, :payment_logs

  validates :user, :property, :share_logs, :payment_logs, presence: true

  def investment_costs
    (sell_quantity * average_share_price_paid).floor
  end

  def sales_proceeds
    # The totals will be negative so we invert it here
    - share_logs.sum(&:total_amount) + property_payment_logs.sum(&:amount)
  end

  def total_fees
    (buy_fees + sell_fees).floor
  end

  def total_dividends
    rent_payment_logs.sum(&:amount)
  end

  def total_income
    0
  end

  def subtotal
    sales_proceeds - investment_costs - total_fees
  end

  private

  def sell_quantity
    # The quantity will be negative so we invert it here
    - share_logs.sum(&:quantity)
  end

  def sell_fees
    return 0 unless property.easy_exit?

    share_logs.collect { |sl| sl.sell_order.total_fees }.sum
  end

  def buy_fees
    # Fee's are on top of investment total
    investment_costs * Share::BuyOrder::FEE_PERCENTAGE
  end

  def average_share_price_paid
    scope = user.share_logs
                .where(property_id: property.id)
                .where('quantity > 0')

    total_shares = scope.sum(:quantity)
    total_paid = scope.sum(:total_amount)

    total_paid.to_d / total_shares
  end

  def rent_payment_logs
    @rent_payment_logs ||= payment_logs.select { |payment_log| payment_log.dividend.payout.type == 'Property::PayoutRent' }
  end

  def property_payment_logs
    @property_payment_logs ||= payment_logs.select { |payment_log| payment_log.dividend.payout.type == 'Property::PayoutProperty' }
  end
end
