class User::Dashboard < ActiveModelSerializers::Model
  attr_accessor :dividends,
                :investments,
                :payment_logs,
                :properties,
                :share_logs,
                :share_orders,
                :user

  def initialize(user, property_ids = Property.ids)
    @user = user
    @share_orders = user.share_orders
                        .includes(property: [:documents, :floorplans, :news_items, :photos, :tags])
                        .where(property_id: property_ids)
                        .order(created_at: :desc)

    @share_logs = user.share_logs
                      .includes(property: [:documents, :floorplans, :news_items, :photos, :tags])
                      .where(property: property_ids)
                      .order(created_at: :desc)

    @dividends = user.payment_logs
                     .where( kind: 'DIVIDEND')
                     .includes(dividend: [:property, :payout])
                     .order(created_at: :desc)

    properties = @share_logs.collect(&:property) + @share_orders.collect(&:property)
    @properties = properties.uniq(&:id).sort_by(&:name)

    @investments = @properties.map do |property|
      dividends = @dividends.select { |pl| pl.dividend.property_id == property.id }
      share_logs = @share_logs.where(property_id: property.id)
      share_orders = @share_orders.where(property_id: property.id)

      User::Investment.new(share_logs, share_orders, dividends, property)
    end
  end

  def annualised_return
    totals = []

    properties.each do |property|
      quantity = total_shares_in_property(property)
      share_value = (property.share_price * quantity).floor

      next if quantity.zero?

      percentage = if property.type == 'Property::Regular'
                     property.hpi + PropertyUtils.rental_yield(property)
                   else
                     property.annualised_return
                   end

      totals << (share_value.to_d / total_value) * (percentage / 100)
    end

    sum = totals.sum

    # Ensure the same data type is always returned
    sum.zero? ? 0.to_d : sum.round(4)
  end

  def profit
    total_value - total_invested + total_dividends
  end

  def total_dividends
    dividends.collect(&:amount).sum
  end

  def total_invested
    share_logs.collect(&:total_amount).sum
  end

  def total_value
    total_value = 0

    properties.each do |property|
      quantity = total_shares_in_property(property)
      total_value += (property.share_price * quantity).floor
    end

    total_value
  end

  def calculate_monthly_deposit_withdraw_summary
    # Filter records from the last 12 months with "Succeeded" status
    succeeded = user.payment_logs.where(status: 'SUCCEEDED').where.not(kind: 'DIVIDEND')
   logs = succeeded.where('created_at >= ?', 12.months.ago)

    # Group logs by month
    grouped_logs = logs.group_by { |log| log.created_at.beginning_of_month }

    # Initialize containers for data
    deposits_data = []
    withdrawals_data = []
    net_data = []
    labels = []
    result = []

    # Process each month's logs
    (11.downto(0)).each do |i|
      current_month = i.months.ago.beginning_of_month
      month_logs = grouped_logs[current_month] || []

      # Calculate values
      deposits = month_logs.select { |log| log.direction == 'credit' }.sum(&:amount)
      withdrawals = month_logs.select { |log| log.direction == 'debit' }.sum(&:amount)

      # Push data
      labels << current_month.strftime('%b %Y')
      deposits_data << deposits
      withdrawals_data << withdrawals
    end
    net_data = deposits_data.zip(withdrawals_data).map { |a, b| a - b }
    net_data.each_with_index do |num, index|
      if index == 0
        result << num
      else
        result << result[index - 1] + num
      end
    end

    # Return formatted output
    {
      "net_deposits": {
        "labels": labels,
        "datasets": [
          {
            "label": "Deposits",
            "data": deposits_data
          },
          {
            "label": "Withdrawals",
            "data": withdrawals_data
          },
          {
            "label": "Net Deposits/Withdrawals",
            "data": result
          }
        ]
      }
    }
  end

  def user_investments_summary
    property_ids = Property.where.not(type: 'Property::Cause').ids
    user_share_logs = ::Share::Log.select('sum(share_logs.quantity) as total,
                         max(share_logs.id),
                         share_logs.property_id,
                         share_logs.user_id')
                                  .includes(:property)
                                  .where(user_id: user.id)
                                  .where(property_id: property_ids)
                                  .group(:property_id)
                                  .sort { |a, b| b.total <=> a.total }
    # Format the datasets
    datasets = user_share_logs.map do |share_log|
      next if share_log.total.zero?
      {
        label: share_log.property.name,
        data: share_log.total
      }
    end
    datasets = datasets.compact

    # Return the formatted output
    {
      "Investments" => datasets
    }
  end

  def monthly_revenue
    # Define the range for the last 12 months
    months = (0..11).map { |i| i.months.ago.end_of_month }

    # Fetch necessary data
    dividends = @dividends.where(status: 'SUCCEEDED', created_at: 11.month.ago.beginning_of_month...Time.now.end_of_month).group_by { |d| d.created_at.end_of_month }

    # Initialize revenue data
    acc_revenue_data = []
    revenue_data = []
    total_dividends = 0
    months.reverse_each do |month|
      # Filter data for the current month
      current_dividends = dividends[month] || []

      current_month_dividend = current_dividends.sum { |dividend| dividend.amount }
      # Calculate total dividends
      total_dividends += current_month_dividend

      revenue_data << { x: month.strftime('%b-%Y'), y: current_month_dividend }
      # Append to result
      acc_revenue_data << { x: month.strftime('%b-%Y'), y: total_dividends }
    end

    {acc_revenue: acc_revenue_data, revenue: revenue_data}
  end

  def return_to_date
    'comming soon'
  end
  private

  def total_shares_in_property(property)
    filtered_logs = share_logs.select { |sl| sl.property_id == property.id }
                              .collect(&:quantity)

    filtered_logs.any? ? filtered_logs.sum : 0
  end
end
