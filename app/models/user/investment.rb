class User::Investment < ActiveModelSerializers::Model
  attr_accessor :dividends, :property, :share_logs, :share_orders

  def initialize(share_logs, share_orders, dividends, property)
    @property = property
    @share_logs = share_logs
    @share_orders = share_orders
    @dividends = dividends
  end

  def total_amount
    (property.share_price * share_logs.collect(&:quantity).sum).floor
  end

  def total_growth
    (total_amount - share_logs.collect(&:total_amount).sum) + property_dividends.collect(&:amount).sum
  end

  def total_income
    rent_dividends.collect(&:amount).sum
  end

  private

  def rent_dividends
    @rent_dividends ||= dividends.select { |payment_log| payment_log.dividend.payout.type == 'Property::PayoutRent' }
  end

  def property_dividends
    @property_dividends ||= dividends.select { |payment_log| payment_log.dividend.payout.type == 'Property::PayoutProperty' }
  end
end
