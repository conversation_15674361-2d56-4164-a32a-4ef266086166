Rails.application.routes.draw do
  mount Rswag::Ui::Engine => '/api-docs'
  mount Rswag::Api::Engine => '/api-docs'

  namespace :api do
    namespace :v1 do
      namespace :certifications do
        resources :levels, only: [:index]
      end

      resources :causes, :properties, only: [:index, :show] do
        get :by_slug, on: :collection
      end

      resources :investment_documents, only: [:index]

      namespace :users do
        resources :passwords,     only: [:create, :show, :update]
        resources :registrations, only: [:create]
        resources :sessions,      only: [:create, :show, :destroy]

        resources :confirmations, only: [:update] do
          collection do
            put :resend
            get :sca_enroll
            get :webhook
          end
        end
        resources :phone_verifications, only: [:create] do
          collection do
            get :verify_otp
          end
        end
        resources :mfa_verifications, only: [:create] do
          collection do
            get :verify_otp
            get :challenge
          end
          member do
            delete :cancel
          end
        end
      end

      resources :users, only: [] do
        resource :contacts,               module: :users, only: [:create]
        resource :dashboard,              module: :users, only: [:show]
        resource :dashboard_two,          module: :users, only: [:show]
        resource :tax_statement,          module: :users, only: [:show]
        resources :wallets,               module: :users, only: [:index]
        resources :payment_logs,          module: :users, only: [:index]
        resources :queued_actions,        module: :users, only: [:index]
        resources :settings,              module: :users, only: [:create] do
          collection do
            post :update_email
            post :update_password
          end
        end
        resources :addresses,             module: :users, only: [:create, :update]


        resources :bank_accounts, module: :users, only: [:index, :create, :show] do
          member do
            delete :deactivate
          end
          collection do
            get :verify_new_account_policy
            get :notifier
          end
        end

        resources :certifications, module: :users, only: [:create] do
          collection do
            put :failed
          end
        end

resources :kycs, module: :users, only: [:create] do
  collection do
    post :create_reference
            post :check
            get :webhook
            get :doc_verification_url
            get :shufti_verification_callback
            get :initiate_shufti_verification
          end
        end

        resources :mandates, module: :users, only: [:index, :create, :show, :update] do
          member do
            delete :cancel
          end
        end

        namespace :payins do
          resources :bank_wires, only: [:create]
          resources :volt_banks, only: [:index, :create, :show] do
            collection do
              get :handle_response
              get :failure
              get :webhook_payment
              get :webhook_account_creation
              get :webhook_batch_credit_transfer
            end
          end

          resources :google_pays, only: [:create] do
            collection do
              get :handle_response
            end
          end
          resources :cards, only: [:create, :update] do
            put :confirm, on: :collection
          end
        end

        namespace :payouts do
          resources :bank_wires, only: [:create]
        end

        resources :potential_investments, module: :users, only: [:create, :show] do
          put :invest, on: :member
        end

        namespace :properties do
          resources :dividends, only: [:index]
        end

        namespace :shares do
          resources :orders, only: [:index, :new, :create, :show] do
            delete :cancel, on: :member
          end

          resources :logs, only: [:index]
        end
      end
    end
  end

  root 'home#index'
end
