default: &default
  adapter: mysql2
  database: '<%= ENV['DATABASE_NAME'] || "uown_#{Rails.env}" %>'
  username: '<%= ENV['DATABASE_USERNAME'] || 'root' %>'
  password: '<%= ENV['DATABASE_PASSWORD'] || 'dub1t' %>'
  host:     <%= ENV['DATABASE_HOST'] || 'localhost' %>
  encoding: utf8
  pool: 5
  timeout: 5000

development:
  <<: *default

production:
  <<: *default

test:
  <<: *default

swagger:
  <<: *default
  adapter: sqlite3
