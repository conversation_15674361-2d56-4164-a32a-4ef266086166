default: &default
  session_timeout: 10
  secret_key_base: <%= ENV["SECRET_KEY_BASE"] %>
  error_environment: <%= ENV["ERROR_ENVIRONMENT"] %>
  frontend_url: <%= ENV["FRONTEND_URL"] %>
  mangopay_sandbox: <%= ENV["MANGOPAY_SANDBOX"] %>
  app_environment: <%= ENV["ENVIRONMENT"] %>
  mangopay_client_id: <%= ENV["MANGOPAY_CLIENT_ID"] %>
  mangopay_client_passphrase: <%= ENV["MANGOPAY_CLIENT_PASSPHRASE"] %>
  legal_user_id: <%= ENV["LEGAL_USER_ID"] %>
  asset_host: <%= ENV["ASSET_HOST"] %>
  site_url: <%= ENV["SITE_URL"] %>
  posthog_api_key: <%= ENV["POSTHOG_API_KEY"] %>
  telnyx_api_key: <%= ENV["TELNYX_API_KEY"] %>
  twilio_account_sid: <%= ENV["TWILIO_ACCOUNT_SID"] %>
  twilio_auth_token: <%= ENV["TWILIO_AUTH_TOKEN"] %>
  twilio_service_id: <%= ENV["TWILIO_SERVICE_ID"] %>
  nuapay_api_key: <%= ENV["NUAPAY_API_KEY"] %>
  nuapay_endpoint: <%= ENV["NUAPAY_ENDPOINT"] %>
  nuapay_webhook_secret: <%= ENV["NUAPAY_WEBHOOK_SECRET"] %>
  nuapay:
    private_key: <%= ENV["NUAPAY_PRIVATE_KEY"] %>
    kid: <%= ENV["NUAPAY_KID"] %>
    cn: <%= ENV["NUAPAY_CN"] %>
    sandbox_url: <%= ENV["NUAPAY_SANDBOX_URL"] %>
  volt:
    endpoint: <%= ENV["VOLT_ENDPOINT"] %>
    client_id: <%= ENV["VOLT_CLIENT_ID"] %>
    secret: <%= ENV["VOLT_CLIENT_SECRET"] %>
    username: <%= ENV["VOLT_USERNAME"] %>
    password: <%= ENV["VOLT_PASSWORD"] %>
    customer_id: <%= ENV["VOLT_CUSTOMER_ID"] %>
    notification_secret: <%= ENV["VOLT_NOTIFICATION_SECRET"] %>
  shufti:
    client_id: <%= ENV["SHUFTI_CLIENT_ID"] %>
    secret_key: <%= ENV["SHUFTI_SECRET_KEY"] %>
    base_url: <%= ENV["SHUFTI_BASE_URL"] %>
  email:
    admin_address: <%= ENV["EMAIL_ADMIN"] %>
    from_address: <%= ENV["EMAIL_FROM"] %>
    domain: <%= ENV["EMAIL_DOMAIN"] %>
    host: <%= ENV["EMAIL_HOST"] %>
    port: <%= ENV["EMAIL_PORT"] %>
    username: <%= ENV["EMAIL_USERNAME"] %>
    password: <%= ENV["EMAIL_PASSWORD"] %>
  s3:
    access_key: <%= ENV["S3_ACCESS_KEY"] %>
    bucket: <%= ENV["S3_BUCKET"] %>
    region: <%= ENV["S3_REGION"] %>
    secret_key: <%= ENV["S3_SECRET_KEY"] %>
  flg_crm:
    base_url: <%= ENV["FLG_BASE_URL"] %>
    upsert_endpoint: <%= ENV["FLG_UPSERT_ENDPOINT"] %>
    api_key: <%= ENV["FLG_API_KEY"] %>
    lead_group_id: <%= ENV["FLG_LEAD_GROUP_ID"] %>
    site_id: <%= ENV["FLG_SITE_ID"] %>
    csv_file_name: <%= ENV["FLG_CSV_FILE_NAME"] %>

development:
  <<: *default
  session_timeout: 240
  secret_key_base: c1a93c97d7e791298847d15f0e558e208aa8fca0ab30817b9073690138d09a2a668e5f0b61ba138914a60b08344b9a2cfe7173cfd775ec52993bcd137745c411
  error_environment: 'development'
  nuapay_webhook_secret: 'ZOaaend5vf'
  frontend_url: <%= ENV["FRONTEND_URL"] || 'http://localhost:3000' %>
  mangopay_sandbox: <%= ENV["MANGOPAY_SANDBOX"] || true %>
  app_environment:  'development'
  mangopay_client_id: <%= ENV["MANGOPAY_CLIENT_ID"] || 'uown' %>
  mangopay_client_passphrase: <%= ENV["MANGOPAY_CLIENT_PASSPHRASE"] || 'Tp2TNP27OpVfwYo7P47TRiZ54xJ2z4fZZmVGAd6Jd3x5KErWcH' %>
  legal_user_id: ********
  site_url: <%= ENV["SITE_URL"] || 'http://localhost:4000' %>
  posthog_api_key: 'phc_Bw6vfs5tnvJF6RRRpJkZzaPdAmEnuNcxFTc7g1jVzSD'
  telnyx_api_key: '**********************************************************'
  twilio_account_sid: '**********************************'
  twilio_auth_token: 'a4585293174555929ec17b3be6d19ed4'
  twilio_service_id: 'VA679d794cea71d7e8a60f78ce25cde9e0'
  nuapay_api_key: '2ca5723f142fe311135d9de414c83fcd1f13b9a2d8d1b081e12975de30a3e739'
  nuapay_endpoint: 'https://sandbox.nuapay.com'
  nuapay:
    private_key: 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
    kid: '244316496579'
    cn: 'a2aej7wzbw'
    sandbox_url: 'https://sandbox.nuapay.com'
  volt:
    endpoint: 'https://gateway.sandbox.volt.io'
    client_id: 'd2c0188d-d7e5-4678-a9e7-d4b24ccf342f'
    secret: '14145351-1045-4a36-b5c4-917bc9d6e124'
    username: '<EMAIL>'
    password: '********************************'
    customer_id: '322a5d18-376a-4c62-a868-8a93c521250f'
    notification_secret: '8208bacb-102a-4af3-ab48-e4b29ba5540b'

  shufti:
    client_id: '2a8aa6955c9b34f88fa746d1c57a7da3087153adb24497f3881e9ac41a3cb4ba'
    secret_key: '********************************'
    base_url: 'https://api.shuftipro.com'
  email:
    admin_address: <EMAIL>
    from_address: 'UOWN Development<<EMAIL>>'
    domain: uown.cloud
    host: 'email-smtp.eu-west-2.amazonaws.com'
    port: 587
    username: ********************
    password: BIrJzaVCc5lcw1MXTi7DZkoK5TgeEbI9uS1uDVf2kLdc
  s3:
    access_key: ********************
    bucket: uown-development-webservices
    region: eu-west-2
    secret_key: xBM33ga4DyqeWq8nR717WnEnAR2dhMwj2+60rud2
  flg_crm:
    base_url: <%= ENV["FLG_BASE_URL"] || 'https://parklane.flg360.co.uk/api' %>
    upsert_endpoint: <%= ENV["FLG_UPSERT_ENDPOINT"] || '/APILeadCreateUpdate.php' %>
    api_key: <%= ENV["FLG_API_KEY"] || 'YRqlF9vREMrlbqmjTZJOOHntHQ9EfKuh' %>
    lead_group_id: <%= ENV["FLG_LEAD_GROUP_ID"] || '61560' %>
    site_id: <%= ENV["FLG_SITE_ID"] || '21162' %>
    csv_file_name: <%= ENV["FLG_CSV_FILE_NAME"] || '20240613-120635-ExportedLeads.csv' %>

test:
  <<: *default
  secret_key_base: 1e5dde4bb1c1d59192aafbb000e3acf13dd3b16fefb362bae70961b5d3605f7823da2b37bd1e092e15561b9dcfb7a08386be6bdabb4e94a2ddd87be68c74268d
  error_environment: 'test'
  frontend_url: 'http://localhost:3000'
  mangopay_sandbox: true
  mangopay_client_id: 'dubit'
  mangopay_client_passphrase: '1234567890abcdefghijklmnopqrstuvwxyz'
  legal_user_id: 54321
  site_url: 'https://uown.test'
  email:
    admin_address: '<EMAIL>'
    from_address: '<EMAIL>'
    domain: 'dubitcloud.com'
    host: 'email-smtp.us-east-1.amazonaws.com'
    port: 587
    username: '********************'
    password: 'ArxfOmvD27b9H9oQ3jtRGAdkdC992ff8/2Ik08BFmmqi'
  telnyx_api_key: '123456'
  twilio_account_sid: '123456'
  twilio_auth_token: '123456'
  twilio_service_id: '123456'
  nuapay_api_key: '123456'
  nuapay_endpoint: 'https://sandbox.nuapay.com'
  flg_crm:
    base_url: 'https://parklane.flg360.co.uk/api'
    upsert_endpoint: '/APILeadCreateUpdate.php'
    api_key: 'YRqlF9vREMrlbqmjTZJOOHntHQ9EfKuh'
    lead_group_id: '61560'
    site_id: '21162'
    csv_file_name: 'test-flg-users.csv'
  nuapay_api_key: '123'
  nuapay_endpoint: 'https://example.uown.co/dev/'
  nuapay:
    private_key: 'cHJpdmF0ZSBrZXkgdG8gbnVhcGF5'
    kid: '456'
    cn: '789'
    sandbox_url: 'https://sandbox.nuapay.com'

production:
  <<: *default

swagger:
  <<: *default
  secret_key_base: 1e5dde4bb1c1d59192aafbb000e3acf13dd3b16fefb362bae70961b5d3605f7823da2b37bd1e092e15561b9dcfb7a08386be6bdabb4e94a2ddd87be68c74268d
  telnyx_api_key: '123456'
  twilio_account_sid: '123456'
  twilio_auth_token: '123456'
  twilio_service_id: '123456'