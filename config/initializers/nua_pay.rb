require 'faraday'
require 'base64'


encoded_api_key = Base64.strict_encode64("#{Rails.application.secrets.nuapay_api_key}")
NuapayClient = Faraday.new(url: Rails.application.secrets.nuapay_endpoint) do |faraday|
  faraday.request  :json
  faraday.adapter  Faraday::Adapter::NetHttp
  faraday.headers['Content-Type'] = 'application/json'
  faraday.headers['Accept'] = 'application/json'
  faraday.headers['Authorization'] = "Basic #{encoded_api_key}"
end