require_relative './filter_parameter_logging'

# Configure Errbit exception monitoring service
Airbrake.configure do |config|
  config.project_id = 564738
  config.project_key = 'bc9e34135d652f4bb70ca7640c12fb71'

  # Filter out sensitive parameters from those log when an exception occurs
  config.blocklist_keys = Rails.application.config.filter_parameters

  config.environment =  Rails.application.secrets.error_environment

  config.ignore_environments = %w[development test]
end
