# Add your own tasks in files placed in lib/tasks ending in .rake,
# for example lib/tasks/capistrano.rake, and they will automatically be available to Rake.

require File.expand_path('../config/application', __FILE__)

Rails.application.load_tasks

# Documentation
require 'sdoc' # and use your RDoc task the same way you used it before
require 'rdoc/task' # ensure this file is also required in order to use `RDoc::Task`

RDoc::Task.new do |rdoc|
  rdoc.generator = 'sdoc'
  rdoc.main = 'base_controller.rb'
  rdoc.rdoc_dir = 'doc'
  rdoc.rdoc_files = ['app/controllers/api/v1/']
  rdoc.template = 'sdoc'
  rdoc.title = 'UOWN API'
end
