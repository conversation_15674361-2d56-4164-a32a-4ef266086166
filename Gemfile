source 'https://rubygems.org'

ruby '3.3.6'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem 'rails', '7.0.1'

# Use mysql2 as the database for Active Record
gem 'mysql2', '0.5.4'

# Use <PERSON><PERSON> as the app server
gem 'puma', '~> 5.0'

# JSON Serialization
gem 'active_model_serializers'

gem 'uown_core', git: '*****************:uownco/uown-core.git', tag: 'v6.6.0'
# gem 'uown_core', path: '../uown_core'

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.4.4', require: false

# Error reporting
gem 'airbrake', '~> 13.0.4'

# Timezone Info
gem 'tzinfo-data'

# API Documentation
gem 'rswag', '~> 2.16.0'

# Define CORS headers for different requests
gem 'rack-cors'

# Required for building api docs in dockerfile
gem 'sqlite3', '~> 1.4'

# tracking functionality on the server-side
gem "posthog-ruby"

# policy
gem "pundit"

# Earth location
gem 'geocoder'

gem 'faraday'

gem 'phony_rails'

# MFA
gem 'twilio-ruby'
group :development, :test, :swagger do
  gem 'byebug'
  gem 'factory_bot'
  gem 'rails-controller-testing'
  gem 'rspec-rails', '~> 5.0.2'
  gem 'sdoc'
  gem 'shoulda-matchers', '~> 5.0.0'
  gem 'simplecov'
  gem 'webmock'

  # Linting
  gem 'rubocop-dubit', '~> 1.0.1', require: false
end

group :development do
  # Test Emails
  gem 'letter_opener'

  # Dependency of web-console
  gem 'listen', '~> 3.3'

  # Access an interactive console on exception pages or by calling 'console' anywhere in the code.
  gem 'web-console', '>= 4.1.0'
end
